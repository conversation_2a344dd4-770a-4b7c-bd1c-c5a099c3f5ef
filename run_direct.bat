@echo off
chcp 65001 >nul
title جامع الأخبار من تلغرام - مشكلة رقم الهاتف

echo.
echo ========================================
echo    جامع الأخبار من تلغرام
echo    Telegram News Collector
echo ========================================
echo.

echo ⚠️ مشكلة في رقم الهاتف المستخدم
echo.
echo المشكلة: رقم الهاتف +9647714367586 محظور من تلغرام
echo.
echo الحل: استخدم رقم هاتف آخر مرتبط بحساب تلغرام نشط
echo.
echo الخطوات:
echo 1. احصل على رقم هاتف آخر مرتبط بتلغرام
echo 2. حدث ملف .env بالرقم الجديد
echo 3. أعد تشغيل التطبيق
echo.
echo ========================================
echo   معلومات التطبيق:
echo   🌐 الرابط: http://localhost:5020
echo   🔑 كلمة المرور: yaseraljebori@25m
echo   📱 التطبيق: جامع الأخبار من تلغرام
echo ========================================
echo.

REM محاولة تشغيل التطبيق المبسط
echo 🚀 محاولة تشغيل التطبيق...
echo.

python simple_app.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo.
    echo الأسباب المحتملة:
    echo 1. Python غير مثبت أو غير موجود في PATH
    echo 2. Flask غير مثبت
    echo 3. مشكلة في البيئة
    echo.
    echo الحلول:
    echo 1. تأكد من تثبيت Python
    echo 2. شغل: pip install flask telethon python-dotenv
    echo 3. أعد تشغيل الكمبيوتر
    echo.
)

echo.
echo 📋 ملاحظات مهمة:
echo • رقم الهاتف الحالي محظور من تلغرام
echo • يجب استخدام رقم هاتف آخر
echo • التطبيق يعمل لكن لا يمكنه الاتصال بتلغرام
echo.
pause
