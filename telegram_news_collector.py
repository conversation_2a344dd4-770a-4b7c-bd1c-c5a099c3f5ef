#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جامع الأخبار من تلغرام - يجمع الأخبار من القنوات المشترك فيها
"""

import asyncio
import os
import json
import pandas as pd
from datetime import datetime, timedelta
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat
from dotenv import load_dotenv
import logging

# تحميل متغيرات البيئة
load_dotenv()

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('telegram_news.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class TelegramNewsCollector:
    """جامع الأخبار من تلغرام"""
    
    def __init__(self):
        """تهيئة جامع الأخبار"""
        self.api_id = os.getenv('TELEGRAM_API_ID')
        self.api_hash = os.getenv('TELEGRAM_API_HASH')
        self.phone_number = os.getenv('TELEGRAM_PHONE_NUMBER')
        
        if not all([self.api_id, self.api_hash, self.phone_number]):
            raise ValueError("معلومات تلغرام API مفقودة في ملف .env")
        
        # إنشاء عميل تلغرام
        self.client = TelegramClient('telegram_news_session', self.api_id, self.api_hash)
        
        # إعدادات جمع الأخبار
        self.max_messages = int(os.getenv('MAX_MESSAGES_PER_CHANNEL', 50))
        self.news_keywords = [
            'خبر', 'أخبار', 'عاجل', 'breaking', 'news', 'urgent',
            'تطورات', 'مستجدات', 'تقرير', 'حدث', 'واقعة'
        ]
        
        self.collected_news = []
        self.subscribed_channels = []
        
    async def start_client(self):
        """بدء عميل تلغرام"""
        try:
            await self.client.start(phone=self.phone_number)
            print("✅ تم الاتصال بتلغرام بنجاح")
            logging.info("تم الاتصال بتلغرام بنجاح")
            return True
        except Exception as e:
            print(f"❌ فشل في الاتصال بتلغرام: {e}")
            logging.error(f"فشل في الاتصال بتلغرام: {e}")
            return False
    
    async def get_subscribed_channels(self):
        """جلب القنوات المشترك فيها"""
        try:
            print("🔍 جلب القنوات المشترك فيها...")
            
            channels = []
            async for dialog in self.client.iter_dialogs():
                if isinstance(dialog.entity, Channel):
                    # فلترة القنوات الإخبارية
                    channel_name = dialog.name.lower()
                    channel_username = getattr(dialog.entity, 'username', '') or ''
                    
                    # البحث عن كلمات مفتاحية إخبارية
                    news_indicators = [
                        'news', 'أخبار', 'خبر', 'إخباري', 'نيوز', 'breaking',
                        'عاجل', 'تلفزيون', 'قناة', 'صحيفة', 'جريدة', 'إعلام'
                    ]
                    
                    is_news_channel = any(indicator in channel_name for indicator in news_indicators)
                    
                    channel_info = {
                        'id': dialog.entity.id,
                        'name': dialog.name,
                        'username': channel_username,
                        'participants_count': getattr(dialog.entity, 'participants_count', 0),
                        'is_news_channel': is_news_channel,
                        'last_message_date': dialog.date
                    }
                    
                    channels.append(channel_info)
            
            # ترتيب القنوات (القنوات الإخبارية أولاً)
            channels.sort(key=lambda x: (not x['is_news_channel'], -x['participants_count']))
            
            self.subscribed_channels = channels
            
            print(f"✅ تم جلب {len(channels)} قناة")
            print(f"📰 قنوات إخبارية: {len([c for c in channels if c['is_news_channel']])}")
            
            return channels
            
        except Exception as e:
            print(f"❌ فشل في جلب القنوات: {e}")
            logging.error(f"فشل في جلب القنوات: {e}")
            return []
    
    async def collect_channel_news(self, channel_info, hours_back=24):
        """جمع الأخبار من قناة محددة"""
        try:
            channel_id = channel_info['id']
            channel_name = channel_info['name']
            
            print(f"📰 جمع أخبار من: {channel_name}")
            
            # تحديد الوقت المرجعي
            since_date = datetime.now() - timedelta(hours=hours_back)
            
            messages = []
            message_count = 0
            
            async for message in self.client.iter_messages(
                channel_id, 
                limit=self.max_messages,
                offset_date=since_date
            ):
                if message.text and message_count < self.max_messages:
                    # فلترة الرسائل الإخبارية
                    message_text = message.text.lower()
                    
                    # تحقق من وجود كلمات مفتاحية إخبارية
                    is_news = any(keyword in message_text for keyword in self.news_keywords)
                    
                    # أو إذا كانت الرسالة طويلة (غالباً أخبار)
                    is_long_message = len(message.text) > 100
                    
                    # أو إذا كانت تحتوي على روابط
                    has_links = 'http' in message.text or 'www.' in message.text
                    
                    if is_news or is_long_message or has_links:
                        news_item = {
                            'channel_id': channel_id,
                            'channel_name': channel_name,
                            'channel_username': channel_info.get('username', ''),
                            'message_id': message.id,
                            'text': message.text,
                            'date': message.date,
                            'views': getattr(message, 'views', 0),
                            'forwards': getattr(message, 'forwards', 0),
                            'replies': getattr(message.replies, 'replies', 0) if message.replies else 0,
                            'has_media': bool(message.media),
                            'media_type': str(type(message.media).__name__) if message.media else None,
                            'collected_at': datetime.now()
                        }
                        
                        messages.append(news_item)
                        message_count += 1
            
            print(f"✅ تم جمع {len(messages)} خبر من {channel_name}")
            return messages
            
        except Exception as e:
            print(f"❌ فشل في جمع أخبار {channel_info['name']}: {e}")
            logging.error(f"فشل في جمع أخبار {channel_info['name']}: {e}")
            return []
    
    async def collect_all_news(self, hours_back=24, max_channels=None):
        """جمع الأخبار من جميع القنوات"""
        try:
            print("🚀 بدء جمع الأخبار من جميع القنوات...")
            
            # جلب القنوات المشترك فيها
            channels = await self.get_subscribed_channels()
            
            if not channels:
                print("❌ لا توجد قنوات للجمع منها")
                return []
            
            # تحديد عدد القنوات للمعالجة
            if max_channels:
                channels = channels[:max_channels]
            
            all_news = []
            
            for i, channel in enumerate(channels, 1):
                print(f"\n📊 [{i}/{len(channels)}] معالجة قناة: {channel['name']}")
                
                # جمع الأخبار من القناة
                channel_news = await self.collect_channel_news(channel, hours_back)
                all_news.extend(channel_news)
                
                # توقف قصير لتجنب Rate Limiting
                await asyncio.sleep(1)
            
            self.collected_news = all_news
            
            print(f"\n🎉 تم جمع {len(all_news)} خبر من {len(channels)} قناة")
            logging.info(f"تم جمع {len(all_news)} خبر من {len(channels)} قناة")
            
            return all_news
            
        except Exception as e:
            print(f"❌ فشل في جمع الأخبار: {e}")
            logging.error(f"فشل في جمع الأخبار: {e}")
            return []
    
    def save_to_csv(self, filename=None):
        """حفظ الأخبار في ملف CSV"""
        if not self.collected_news:
            print("❌ لا توجد أخبار للحفظ")
            return None
        
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"telegram_news_{timestamp}.csv"
            
            df = pd.DataFrame(self.collected_news)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            print(f"💾 تم حفظ {len(self.collected_news)} خبر في: {filename}")
            logging.info(f"تم حفظ {len(self.collected_news)} خبر في: {filename}")
            
            return filename
            
        except Exception as e:
            print(f"❌ فشل في حفظ الملف: {e}")
            logging.error(f"فشل في حفظ الملف: {e}")
            return None
    
    def save_to_json(self, filename=None):
        """حفظ الأخبار في ملف JSON"""
        if not self.collected_news:
            print("❌ لا توجد أخبار للحفظ")
            return None
        
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"telegram_news_{timestamp}.json"
            
            # تحويل التواريخ إلى نصوص
            news_data = []
            for item in self.collected_news:
                item_copy = item.copy()
                item_copy['date'] = item_copy['date'].isoformat() if item_copy['date'] else None
                item_copy['collected_at'] = item_copy['collected_at'].isoformat()
                news_data.append(item_copy)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(news_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 تم حفظ {len(self.collected_news)} خبر في: {filename}")
            logging.info(f"تم حفظ {len(self.collected_news)} خبر في: {filename}")
            
            return filename
            
        except Exception as e:
            print(f"❌ فشل في حفظ الملف: {e}")
            logging.error(f"فشل في حفظ الملف: {e}")
            return None
    
    def get_news_summary(self):
        """الحصول على ملخص الأخبار المجمعة"""
        if not self.collected_news:
            return {"total": 0, "channels": 0, "summary": "لا توجد أخبار"}
        
        # إحصائيات الأخبار
        total_news = len(self.collected_news)
        channels_count = len(set(item['channel_name'] for item in self.collected_news))
        
        # أكثر القنوات نشاطاً
        channel_stats = {}
        for item in self.collected_news:
            channel_name = item['channel_name']
            if channel_name not in channel_stats:
                channel_stats[channel_name] = 0
            channel_stats[channel_name] += 1
        
        top_channels = sorted(channel_stats.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # الأخبار الأحدث
        recent_news = sorted(self.collected_news, key=lambda x: x['date'], reverse=True)[:5]
        
        summary = {
            "total": total_news,
            "channels": channels_count,
            "top_channels": top_channels,
            "recent_news": [
                {
                    "channel": item['channel_name'],
                    "text": item['text'][:100] + "..." if len(item['text']) > 100 else item['text'],
                    "date": item['date'].strftime("%Y-%m-%d %H:%M") if item['date'] else "غير محدد"
                }
                for item in recent_news
            ]
        }
        
        return summary
    
    async def close(self):
        """إغلاق الاتصال"""
        try:
            await self.client.disconnect()
            print("✅ تم إغلاق الاتصال بتلغرام")
        except Exception as e:
            print(f"⚠️ خطأ في إغلاق الاتصال: {e}")

# مثال على الاستخدام
async def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء تشغيل جامع الأخبار من تلغرام")
    print("=" * 60)
    
    try:
        # إنشاء جامع الأخبار
        collector = TelegramNewsCollector()
        
        # بدء الاتصال
        if await collector.start_client():
            print("\n🔍 جمع الأخبار من آخر 24 ساعة...")
            
            # جمع الأخبار
            news = await collector.collect_all_news(hours_back=24, max_channels=10)
            
            if news:
                # حفظ النتائج
                csv_file = collector.save_to_csv()
                json_file = collector.save_to_json()
                
                # عرض الملخص
                summary = collector.get_news_summary()
                print(f"\n📊 ملخص الأخبار:")
                print(f"   📰 إجمالي الأخبار: {summary['total']}")
                print(f"   📺 عدد القنوات: {summary['channels']}")
                print(f"   🏆 أكثر القنوات نشاطاً:")
                for channel, count in summary['top_channels']:
                    print(f"      • {channel}: {count} خبر")
                
                print(f"\n📁 الملفات المحفوظة:")
                if csv_file:
                    print(f"   📊 CSV: {csv_file}")
                if json_file:
                    print(f"   📋 JSON: {json_file}")
            
            else:
                print("❌ لم يتم جمع أي أخبار")
        
        else:
            print("❌ فشل في الاتصال بتلغرام")
    
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        logging.error(f"خطأ عام: {e}")
    
    finally:
        # إغلاق الاتصال
        try:
            await collector.close()
        except:
            pass

if __name__ == "__main__":
    # تشغيل الدالة الرئيسية
    asyncio.run(main())
