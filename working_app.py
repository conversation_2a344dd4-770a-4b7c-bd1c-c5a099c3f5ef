#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق ويب يعمل لجمع الأخبار من تلغرام
"""

import asyncio
import os
import json
from datetime import datetime, timedelta
import threading
import time

# محاولة استيراد Flask
try:
    from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
    FLASK_AVAILABLE = True
except ImportError:
    print("❌ Flask غير مثبت. شغل: pip install flask")
    FLASK_AVAILABLE = False

# محاولة استيراد Telethon
try:
    from telethon import TelegramClient
    TELETHON_AVAILABLE = True
except ImportError:
    print("❌ Telethon غير مثبت. شغل: pip install telethon")
    TELETHON_AVAILABLE = False

if not FLASK_AVAILABLE:
    print("🔧 تثبيت Flask...")
    os.system("pip install flask python-dotenv")
    try:
        from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
        FLASK_AVAILABLE = True
        print("✅ تم تثبيت Flask")
    except ImportError:
        print("❌ فشل في تثبيت Flask")
        exit(1)

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'TelegramNews_Ultra_Secure_Key_2024_Yaser_Protection'

# بيانات تلغرام
API_ID = "21311521"
API_HASH = "7e63557fb0d1324435d6c055d363d8f2"
PHONE = "+9647714366758"

# متغيرات عامة
collection_status = {
    'is_running': False,
    'last_update': None,
    'total_news': 0,
    'channels_count': 0,
    'error_message': None
}

latest_news = []
channels_list = []

def check_admin_password(password):
    """التحقق من كلمة مرور الإدارة"""
    return password == 'yaseraljebori@25m'

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html', 
                         collection_status=collection_status,
                         latest_news=latest_news[:10])

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        password = request.form.get('password')
        if check_admin_password(password):
            session['logged_in'] = True
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.pop('logged_in', None)
    flash('تم تسجيل الخروج', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))
    
    return render_template('dashboard.html',
                         collection_status=collection_status,
                         channels_list=channels_list,
                         latest_news=latest_news[:20])

@app.route('/api/start_collection', methods=['POST'])
def start_collection():
    """بدء جمع الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    if collection_status['is_running']:
        return jsonify({'error': 'جمع الأخبار يعمل بالفعل'}), 400
    
    try:
        # بدء جمع الأخبار في خيط منفصل
        thread = threading.Thread(target=run_news_collection)
        thread.daemon = True
        thread.start()
        
        return jsonify({'message': 'تم بدء جمع الأخبار'})
    
    except Exception as e:
        return jsonify({'error': f'فشل في بدء جمع الأخبار: {str(e)}'}), 500

@app.route('/api/stop_collection', methods=['POST'])
def stop_collection():
    """إيقاف جمع الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    collection_status['is_running'] = False
    return jsonify({'message': 'تم إيقاف جمع الأخبار'})

@app.route('/api/status')
def get_status():
    """الحصول على حالة جمع الأخبار"""
    return jsonify(collection_status)

@app.route('/news')
def news_page():
    """صفحة الأخبار"""
    return render_template('news.html', latest_news=latest_news)

@app.route('/setup')
def setup():
    """صفحة الإعداد الأولي"""
    return render_template('setup.html')

@app.route('/api/test_connection', methods=['POST'])
def test_connection():
    """اختبار الاتصال بتلغرام"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    try:
        # اختبار سريع للاتصال
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async def test():
            client = TelegramClient('test_connection', API_ID, API_HASH)
            try:
                await client.start(phone=PHONE)
                me = await client.get_me()
                await client.disconnect()
                return True, f"تم الاتصال بنجاح! مرحباً {me.first_name}"
            except Exception as e:
                await client.disconnect()
                return False, str(e)
        
        success, message = loop.run_until_complete(test())
        
        if success:
            return jsonify({'message': message})
        else:
            return jsonify({'error': f'فشل في الاتصال: {message}'}), 500
    
    except Exception as e:
        return jsonify({'error': f'خطأ في الاتصال: {str(e)}'}), 500

def run_news_collection():
    """تشغيل جمع الأخبار"""
    global collection_status, latest_news, channels_list
    
    try:
        collection_status['is_running'] = True
        collection_status['error_message'] = None
        
        # إنشاء حلقة أحداث جديدة للخيط
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # تشغيل جمع الأخبار
        loop.run_until_complete(collect_news_async())
        
    except Exception as e:
        collection_status['error_message'] = str(e)
        print(f"❌ خطأ في جمع الأخبار: {e}")
    
    finally:
        collection_status['is_running'] = False

async def collect_news_async():
    """جمع الأخبار بشكل غير متزامن"""
    global collection_status, latest_news, channels_list
    
    client = TelegramClient('news_session', API_ID, API_HASH)
    
    try:
        print("🔄 بدء الاتصال بتلغرام...")
        await client.start(phone=PHONE)
        print("✅ تم الاتصال بتلغرام")
        
        # جلب القنوات
        print("📋 جلب قائمة القنوات...")
        channels = []
        news_channels = []
        
        async for dialog in client.iter_dialogs(limit=100):
            if hasattr(dialog.entity, 'broadcast') and dialog.entity.broadcast:
                channel_info = {
                    'name': dialog.name,
                    'id': dialog.entity.id,
                    'username': getattr(dialog.entity, 'username', None),
                    'participants_count': getattr(dialog.entity, 'participants_count', 0)
                }
                channels.append(channel_info)
                
                # فحص إذا كانت قناة إخبارية
                name_lower = dialog.name.lower()
                if any(keyword in name_lower for keyword in ['news', 'أخبار', 'إخبار', 'نيوز', 'bbc', 'cnn', 'الجزيرة', 'العربية', 'شفق', 'فرات', 'صابرين']):
                    news_channels.append(channel_info)
        
        channels_list = channels
        print(f"✅ تم العثور على {len(channels)} قناة، منها {len(news_channels)} إخبارية")
        
        # جمع الأخبار من القنوات الإخبارية
        all_news = []
        hours_back = 24
        
        for channel in news_channels[:10]:  # أول 10 قنوات إخبارية
            try:
                print(f"📰 جمع أخبار من: {channel['name']}")
                
                since_date = datetime.now() - timedelta(hours=hours_back)
                messages = []
                
                async for message in client.iter_messages(channel['id'], limit=20):
                    if message.date < since_date:
                        break
                    
                    if message.text and len(message.text) > 30:
                        # فلترة المحتوى الإخباري
                        text_lower = message.text.lower()
                        if any(keyword in text_lower for keyword in ['خبر', 'أخبار', 'عاجل', 'breaking', 'news', 'تطورات']):
                            news_item = {
                                'channel_name': channel['name'],
                                'channel_username': channel['username'],
                                'text': message.text,
                                'date': message.date.isoformat(),
                                'message_id': message.id,
                                'views': getattr(message, 'views', 0),
                                'forwards': getattr(message, 'forwards', 0),
                                'has_media': bool(message.media),
                                'media_type': type(message.media).__name__ if message.media else None
                            }
                            all_news.append(news_item)
                
                print(f"   ✅ تم جمع {len(messages)} رسالة")
                
            except Exception as e:
                print(f"   ❌ خطأ في جمع أخبار {channel['name']}: {e}")
        
        # ترتيب الأخبار حسب التاريخ
        all_news.sort(key=lambda x: x['date'], reverse=True)
        
        # تحديث البيانات العامة
        latest_news = all_news
        collection_status['total_news'] = len(all_news)
        collection_status['channels_count'] = len(news_channels)
        collection_status['last_update'] = datetime.now().isoformat()
        
        print(f"🎉 تم جمع {len(all_news)} خبر من {len(news_channels)} قناة")
        
    except Exception as e:
        print(f"❌ خطأ في جمع الأخبار: {e}")
        raise e
    
    finally:
        await client.disconnect()
        print("✅ تم إغلاق الاتصال بتلغرام")

if __name__ == '__main__':
    print("🚀 بدء تشغيل تطبيق جمع الأخبار من تلغرام")
    print("=" * 60)
    print("🌐 الواجهة متاحة على: http://localhost:5020")
    print("🔑 كلمة مرور الإدارة: yaseraljebori@25m")
    print("📞 رقم الهاتف: +9647714366758")
    print("=" * 60)
    
    if not TELETHON_AVAILABLE:
        print("⚠️ Telethon غير مثبت - سيتم تثبيته...")
        os.system("pip install telethon")
    
    # تشغيل التطبيق
    app.run(host='0.0.0.0', port=5020, debug=True)
