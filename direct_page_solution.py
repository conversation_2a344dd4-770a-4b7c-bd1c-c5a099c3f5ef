#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل مباشر: استخدام معرف الصفحة مع User Token
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

def test_your_page_directly():
    """اختبار الوصول لصفحتك مباشرة"""
    print("🎯 اختبار الوصول لصفحتك مباشرة")
    print("=" * 60)
    
    # معرف صفحتك
    your_page_id = "61577045007969"
    user_token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    
    if not user_token:
        print("❌ لا يوجد User Access Token")
        return False
    
    print(f"📄 صفحتك: {your_page_id}")
    print(f"🔑 استخدام User Token الحالي")
    
    # اختبار الوصول لصفحتك
    try:
        # معلومات الصفحة
        page_url = f"https://graph.facebook.com/v18.0/{your_page_id}"
        page_params = {
            'access_token': user_token,
            'fields': 'id,name,category,about,fan_count'
        }
        
        print(f"\n🔍 اختبار الوصول لمعلومات صفحتك...")
        page_response = requests.get(page_url, params=page_params, timeout=10)
        
        if page_response.status_code == 200:
            page_data = page_response.json()
            print(f"✅ نجح الوصول لصفحتك!")
            print(f"   📝 الاسم: {page_data.get('name', 'غير محدد')}")
            print(f"   🆔 المعرف: {page_data.get('id', 'غير محدد')}")
            print(f"   📂 الفئة: {page_data.get('category', 'غير محدد')}")
            print(f"   👥 المتابعون: {page_data.get('fan_count', 0)}")
            
            # اختبار منشورات صفحتك
            print(f"\n🔍 اختبار الوصول لمنشورات صفحتك...")
            posts_url = f"https://graph.facebook.com/v18.0/{your_page_id}/posts"
            posts_params = {
                'access_token': user_token,
                'fields': 'id,message,created_time,type',
                'limit': 5
            }
            
            posts_response = requests.get(posts_url, params=posts_params, timeout=10)
            
            if posts_response.status_code == 200:
                posts_data = posts_response.json()
                posts = posts_data.get('data', [])
                print(f"✅ يمكن الوصول لمنشورات صفحتك! ({len(posts)} منشور)")
                
                for i, post in enumerate(posts, 1):
                    message = post.get('message', 'بدون نص')
                    if message:
                        preview = message[:50] + "..." if len(message) > 50 else message
                        print(f"   {i}. {preview}")
                
                return True
                
            else:
                posts_error = posts_response.json() if posts_response.content else {}
                posts_msg = posts_error.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"❌ لا يمكن الوصول لمنشورات صفحتك: {posts_msg}")
                return False
                
        else:
            page_error = page_response.json() if page_response.content else {}
            page_msg = page_error.get('error', {}).get('message', 'خطأ غير معروف')
            print(f"❌ لا يمكن الوصول لصفحتك: {page_msg}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def create_working_solution():
    """إنشاء حل عملي بالصلاحيات المتاحة"""
    print(f"\n🔧 إنشاء حل عملي...")
    print("=" * 60)
    
    your_page_id = "61577045007969"
    
    # قائمة صفحات للاختبار (بما فيها صفحتك)
    test_pages = [
        {
            'id': your_page_id,
            'name': 'صفحتك (iNews)',
            'type': 'own_page'
        },
        {
            'id': 'facebook',
            'name': 'Facebook',
            'type': 'public_page'
        },
        {
            'id': 'meta',
            'name': 'Meta',
            'type': 'public_page'
        }
    ]
    
    user_token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    working_pages = []
    
    for page in test_pages:
        print(f"\n📄 اختبار: {page['name']} ({page['id']})")
        
        try:
            # اختبار الوصول للصفحة
            url = f"https://graph.facebook.com/v18.0/{page['id']}"
            params = {
                'access_token': user_token,
                'fields': 'id,name,category'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ يمكن الوصول للصفحة")
                
                # اختبار المنشورات
                posts_url = f"https://graph.facebook.com/v18.0/{page['id']}/posts"
                posts_params = {
                    'access_token': user_token,
                    'fields': 'id,message,created_time',
                    'limit': 3
                }
                
                posts_response = requests.get(posts_url, params=posts_params, timeout=10)
                
                if posts_response.status_code == 200:
                    posts_data = posts_response.json()
                    posts = posts_data.get('data', [])
                    print(f"   ✅ يمكن الوصول للمنشورات ({len(posts)} منشور)")
                    
                    working_pages.append({
                        'id': page['id'],
                        'name': data.get('name', page['name']),
                        'type': page['type'],
                        'posts_available': True,
                        'posts_count': len(posts)
                    })
                    
                else:
                    print(f"   ❌ لا يمكن الوصول للمنشورات")
                    working_pages.append({
                        'id': page['id'],
                        'name': data.get('name', page['name']),
                        'type': page['type'],
                        'posts_available': False,
                        'posts_count': 0
                    })
                    
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"   ❌ لا يمكن الوصول: {error_msg}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
    
    return working_pages

def update_app_config(working_pages):
    """تحديث إعدادات التطبيق"""
    print(f"\n🔄 تحديث إعدادات التطبيق...")
    print("=" * 60)
    
    # فلترة الصفحات التي تعمل
    accessible_pages = [p for p in working_pages if p['posts_available']]
    
    if accessible_pages:
        print(f"✅ صفحات متاحة للاستخدام ({len(accessible_pages)}):")
        for page in accessible_pages:
            print(f"   📄 {page['name']} - {page['posts_count']} منشور")
        
        # حفظ الإعدادات
        config = {
            'working_pages': accessible_pages,
            'your_page_id': '61577045007969',
            'last_updated': '2024-12-19',
            'status': 'working'
        }
        
        try:
            with open('page_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ تم حفظ الإعدادات في page_config.json")
            return True
            
        except Exception as e:
            print(f"❌ فشل في حفظ الإعدادات: {e}")
            return False
    
    else:
        print(f"❌ لا توجد صفحات متاحة")
        return False

def suggest_next_steps(working_pages):
    """اقتراح الخطوات التالية"""
    print(f"\n📋 الخطوات التالية:")
    print("=" * 60)
    
    accessible_pages = [p for p in working_pages if p['posts_available']]
    
    if accessible_pages:
        print(f"🎉 **لديك {len(accessible_pages)} صفحة تعمل!**")
        print(f"")
        print(f"1️⃣ **شغل التطبيق:**")
        print(f"   🚀 python app.py")
        print(f"   🌐 اذهب لـ: http://localhost:5020")
        print(f"")
        print(f"2️⃣ **جرب جمع الأخبار:**")
        print(f"   • استخدم الصفحات المتاحة")
        print(f"   • ابدأ بصفحتك الشخصية")
        print(f"   • أضف منشورات أكثر لصفحتك")
        print(f"")
        print(f"3️⃣ **لتحسين النتائج:**")
        print(f"   • أضف منشورات أخبار لصفحتك")
        print(f"   • شارك روابط أخبار من مصادر مختلفة")
        print(f"   • ادع أصدقاء للإعجاب بصفحتك")
        
    else:
        print(f"❌ **لا توجد صفحات متاحة حالياً**")
        print(f"")
        print(f"🔧 **الحلول البديلة:**")
        print(f"")
        print(f"1️⃣ **أضف محتوى لصفحتك:**")
        print(f"   • انشر منشورات أخبار في صفحتك")
        print(f"   • شارك روابط من مواقع أخبار")
        print(f"   • اكتب تحديثات إخبارية")
        print(f"")
        print(f"2️⃣ **جرب RSS feeds:**")
        print(f"   • مصادر أخبار عبر RSS")
        print(f"   • لا يحتاج صلاحيات Facebook")
        print(f"   • أسهل في التنفيذ")
        print(f"")
        print(f"3️⃣ **استخدم مصادر أخرى:**")
        print(f"   • Twitter API")
        print(f"   • مواقع أخبار مباشرة")
        print(f"   • خدمات أخبار مجانية")

def main():
    """الدالة الرئيسية"""
    print("🎯 حل مباشر: استخدام صفحتك مع User Token")
    print("=" * 60)
    print("📄 صفحتك: 61577045007969")
    print("🔑 استخدام الصلاحيات المتاحة")
    print("=" * 60)
    
    # اختبار صفحتك مباشرة
    if test_your_page_directly():
        print(f"\n🎉 ممتاز! يمكن الوصول لصفحتك")
    
    # إنشاء حل عملي
    working_pages = create_working_solution()
    
    # تحديث إعدادات التطبيق
    if update_app_config(working_pages):
        print(f"\n✅ تم تحديث إعدادات التطبيق")
    
    # اقتراح الخطوات التالية
    suggest_next_steps(working_pages)
    
    print(f"\n💡 **الخلاصة:**")
    print(f"   • صفحتك: 61577045007969")
    print(f"   • User Token يعمل مع صفحتك")
    print(f"   • أضف محتوى أكثر لصفحتك")
    print(f"   • جرب التطبيق مع الصفحات المتاحة")

if __name__ == "__main__":
    main()
