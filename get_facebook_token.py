#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الحصول على User Access Token من Facebook مع الصلاحيات المطلوبة
"""

import webbrowser
import requests
import os
from dotenv import load_dotenv

load_dotenv()

def open_graph_api_explorer():
    """فتح Graph API Explorer للحصول على User Access Token"""
    print("🚀 الحصول على User Access Token من Facebook")
    print("=" * 60)
    
    app_id = os.getenv('FACEBOOK_APP_ID')
    
    if not app_id:
        print("❌ لا يوجد App ID في ملف .env")
        return False
    
    print(f"📱 App ID: {app_id}")
    print(f"🎯 الهدف: الحصول على User Access Token مع الصلاحيات المطلوبة")
    print("=" * 60)
    
    # بناء رابط Graph API Explorer
    base_url = "https://developers.facebook.com/tools/explorer"
    url = f"{base_url}/{app_id}/?method=GET&path=me%2Ffeed&version=v18.0"
    
    try:
        print("🔗 فتح Graph API Explorer...")
        webbrowser.open(url)
        print("✅ تم فتح الرابط في المتصفح")
        
        print(f"\n📋 الخطوات المطلوبة في Graph API Explorer:")
        print("=" * 60)
        
        print("1️⃣ **تأكد من اختيار تطبيقك:**")
        print(f"   • يجب أن يظهر App ID: {app_id}")
        print(f"   • إذا لم يظهر، اختره من القائمة المنسدلة")
        
        print(f"\n2️⃣ **أضف الصلاحيات المطلوبة:**")
        print(f"   • اضغط 'Add a Permission' أو 'Get Token'")
        print(f"   • أضف هذه الصلاحيات:")
        
        permissions = [
            'public_profile',
            'user_posts', 
            'user_likes',
            'pages_show_list',
            'pages_read_engagement'
        ]
        
        for perm in permissions:
            print(f"     ✓ {perm}")
        
        print(f"\n3️⃣ **احصل على الـ Token:**")
        print(f"   • اضغط 'Generate Access Token'")
        print(f"   • سجل الدخول بحساب Facebook")
        print(f"   • وافق على جميع الصلاحيات المطلوبة")
        
        print(f"\n4️⃣ **انسخ الـ Access Token:**")
        print(f"   • انسخ الـ Token الطويل الذي يظهر")
        print(f"   • يبدأ عادة بـ EAAB...")
        
        print(f"\n⚠️ **ملاحظات مهمة:**")
        print(f"   • بعض الصلاحيات قد تحتاج موافقة Facebook")
        print(f"   • إذا لم تظهر صلاحية، جرب بدونها أولاً")
        print(f"   • الـ Token صالح لمدة محدودة (ساعة أو أكثر)")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في فتح المتصفح: {e}")
        print(f"🔗 افتح هذا الرابط يدوياً:")
        print(f"{url}")
        return False

def save_user_token():
    """حفظ User Access Token في ملف .env"""
    print(f"\n💾 حفظ User Access Token")
    print("=" * 60)
    
    token = input("🔑 الصق الـ User Access Token هنا: ").strip()
    
    if not token:
        print("❌ لم يتم إدخال token")
        return False
    
    if len(token) < 50:
        print("⚠️ تحذير: الـ Token قصير جداً، تأكد من صحته")
    
    try:
        # قراءة ملف .env
        env_file = ".env"
        lines = []
        
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        
        # تحديث FACEBOOK_USER_ACCESS_TOKEN
        token_line = f"FACEBOOK_USER_ACCESS_TOKEN={token}\n"
        found = False
        
        for i, line in enumerate(lines):
            if line.startswith('FACEBOOK_USER_ACCESS_TOKEN='):
                lines[i] = token_line
                found = True
                break
        
        if not found:
            lines.append(token_line)
        
        # حفظ الملف
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم حفظ الـ Token في ملف .env")
        
        # إعادة تحميل متغيرات البيئة
        load_dotenv(override=True)
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في حفظ الـ Token: {e}")
        return False

def test_token():
    """اختبار الـ User Access Token"""
    print(f"\n🧪 اختبار User Access Token")
    print("=" * 60)
    
    token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    
    if not token:
        print("❌ لا يوجد User Access Token للاختبار")
        return False
    
    print(f"🔍 اختبار الـ Token...")
    
    # اختبارات مختلفة
    tests = [
        {
            'name': 'معلومات المستخدم',
            'url': 'https://graph.facebook.com/v18.0/me',
            'params': {'fields': 'id,name,email'}
        },
        {
            'name': 'الصلاحيات المتاحة',
            'url': 'https://graph.facebook.com/v18.0/me/permissions',
            'params': {}
        },
        {
            'name': 'الصفحات المعجب بها',
            'url': 'https://graph.facebook.com/v18.0/me/likes',
            'params': {'limit': 5}
        },
        {
            'name': 'خلاصة المستخدم',
            'url': 'https://graph.facebook.com/v18.0/me/feed',
            'params': {'limit': 3}
        }
    ]
    
    successful_tests = 0
    available_permissions = []
    
    for test in tests:
        print(f"\n🔍 اختبار: {test['name']}")
        
        try:
            params = {'access_token': token}
            params.update(test['params'])
            
            response = requests.get(test['url'], params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if test['name'] == 'الصلاحيات المتاحة':
                    permissions = data.get('data', [])
                    granted = [p['permission'] for p in permissions if p.get('status') == 'granted']
                    available_permissions = granted
                    print(f"   ✅ الصلاحيات المتاحة ({len(granted)}):")
                    for perm in granted:
                        print(f"     • {perm}")
                
                elif 'data' in data:
                    items = data['data']
                    print(f"   ✅ نجح! عدد العناصر: {len(items)}")
                    if items and 'name' in items[0]:
                        print(f"   📝 مثال: {items[0]['name']}")
                else:
                    print(f"   ✅ نجح! البيانات: {data.get('name', 'متوفر')}")
                
                successful_tests += 1
                
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"   ❌ فشل: {error_msg}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
    
    # تقييم النتائج
    print(f"\n📊 ملخص النتائج:")
    print("=" * 60)
    print(f"✅ نجح {successful_tests}/{len(tests)} اختبار")
    
    if successful_tests >= 2:
        print(f"🎉 الـ Token يعمل بشكل جيد!")
        
        # فحص الصلاحيات المطلوبة
        required_permissions = ['user_posts', 'user_likes', 'pages_show_list']
        missing = [p for p in required_permissions if p not in available_permissions]
        
        if missing:
            print(f"⚠️ صلاحيات مفقودة: {', '.join(missing)}")
            print(f"💡 يمكن المحاولة بالصلاحيات المتاحة أولاً")
        else:
            print(f"🎯 جميع الصلاحيات الأساسية متوفرة!")
        
        return True
    else:
        print(f"❌ الـ Token يحتاج تحسين أو إعادة إنشاء")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔑 الحصول على Facebook User Access Token")
    print("=" * 60)
    print("🎯 الهدف: الحصول على صلاحيات للوصول للصفحات والأخبار")
    print("=" * 60)
    
    # فتح Graph API Explorer
    if open_graph_api_explorer():
        input(f"\n⏸️ اضغط Enter بعد الحصول على الـ Access Token...")
        
        # حفظ الـ Token
        if save_user_token():
            # اختبار الـ Token
            if test_token():
                print(f"\n🎉 تم! يمكنك الآن استخدام التطبيق")
                print(f"🚀 شغل التطبيق: python app.py")
                print(f"🌐 اذهب لـ: http://localhost:5020")
            else:
                print(f"\n⚠️ الـ Token يعمل جزئياً، جرب التطبيق")
        else:
            print(f"\n❌ فشل في حفظ الـ Token")
    else:
        print(f"\n❌ فشل في فتح Graph API Explorer")
    
    print(f"\n📋 الخطوات التالية:")
    print(f"1. شغل التطبيق: python app.py")
    print(f"2. اذهب لـ: http://localhost:5020")
    print(f"3. جرب جمع الأخبار من لوحة التحكم")

if __name__ == "__main__":
    main()
