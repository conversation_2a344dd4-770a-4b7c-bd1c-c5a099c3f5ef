"""
اختبار جمع الأخبار من Facebook
"""

from inews_collector import iNewsCollector
import json

def test_news_collection():
    """اختبار جمع الأخبار"""
    
    print("🔍 اختبار جمع الأخبار من Facebook...")
    
    try:
        # إنشاء جامع الأخبار
        collector = iNewsCollector()
        
        # اختبار الاتصال
        print("1️⃣ اختبار الاتصال...")
        if collector.get_access_token():
            print(f"✅ تم الحصول على Access Token: {collector.access_token[:30]}...")
        else:
            print("❌ فشل في الحصول على Access Token")
            return
        
        # اختبار البحث عن صفحات
        print("\n2️⃣ البحث عن صفحات أخبار...")
        search_results = collector.search_news_pages("news", 5)
        
        if search_results:
            print(f"✅ تم العثور على {len(search_results)} صفحة:")
            for page in search_results:
                print(f"  📄 {page.get('name')} (ID: {page.get('id')})")
        else:
            print("❌ لم يتم العثور على صفحات")
        
        # اختبار جمع الأخبار من صفحة محددة
        print("\n3️⃣ اختبار جمع الأخبار من صفحة محددة...")
        
        # جرب صفحات مختلفة
        test_pages = [
            "yallaiq",  # الصفحة المضافة
            "BBCArabic",
            "aljazeera",
            "6abc",  # صفحة أخبار عامة
            "cnn"
        ]
        
        for page_id in test_pages:
            print(f"\n🔍 اختبار الصفحة: {page_id}")
            
            # معلومات الصفحة
            page_info = collector.get_page_info(page_id)
            if page_info:
                print(f"✅ معلومات الصفحة: {page_info.get('name', 'غير محدد')}")
                print(f"   التصنيف: {page_info.get('category', 'غير محدد')}")
                print(f"   المتابعون: {page_info.get('fan_count', 'غير محدد')}")
                
                # جلب المنشورات
                posts = collector.get_public_posts(page_id, 3)
                if posts:
                    print(f"✅ تم جلب {len(posts)} منشور")
                    for i, post in enumerate(posts, 1):
                        message = post.get('message', post.get('story', 'لا يوجد نص'))
                        print(f"   {i}. {message[:100]}...")
                    
                    # جمع الأخبار وحفظها
                    news_data = collector.collect_news_from_pages([page_id], 3)
                    if news_data:
                        filename = collector.save_to_json(news_data, f"test_news_{page_id}.json", secure=False)
                        print(f"✅ تم حفظ {len(news_data)} خبر في {filename}")
                        return True
                else:
                    print("❌ لم يتم جلب أي منشورات")
            else:
                print("❌ فشل في جلب معلومات الصفحة")
        
        print("\n❌ لم يتم جمع أي أخبار من جميع الصفحات المختبرة")
        return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_facebook_api_directly():
    """اختبار Facebook API مباشرة"""
    
    print("\n🔧 اختبار Facebook API مباشرة...")
    
    import requests
    from security import security_manager
    
    try:
        app_id, app_secret = security_manager.get_facebook_credentials()
        
        # الحصول على Access Token
        token_url = "https://graph.facebook.com/v18.0/oauth/access_token"
        params = {
            'client_id': app_id,
            'client_secret': app_secret,
            'grant_type': 'client_credentials'
        }
        
        response = requests.get(token_url, params=params)
        if response.status_code == 200:
            data = response.json()
            access_token = data.get('access_token')
            print(f"✅ Access Token: {access_token[:30]}...")
            
            # اختبار البحث
            search_url = "https://graph.facebook.com/v18.0/search"
            search_params = {
                'q': 'news',
                'type': 'page',
                'fields': 'id,name,category,fan_count',
                'limit': 10,
                'access_token': access_token
            }
            
            search_response = requests.get(search_url, params=search_params)
            print(f"🔍 البحث - Status: {search_response.status_code}")
            print(f"🔍 البحث - Response: {search_response.text[:200]}...")
            
            if search_response.status_code == 200:
                search_data = search_response.json()
                pages = search_data.get('data', [])
                print(f"✅ تم العثور على {len(pages)} صفحة")
                
                # اختبار جلب منشورات من أول صفحة
                if pages:
                    page_id = pages[0]['id']
                    page_name = pages[0]['name']
                    print(f"\n📄 اختبار جلب منشورات من: {page_name} ({page_id})")
                    
                    posts_url = f"https://graph.facebook.com/v18.0/{page_id}/posts"
                    posts_params = {
                        'fields': 'id,message,story,created_time,type,link',
                        'limit': 5,
                        'access_token': access_token
                    }
                    
                    posts_response = requests.get(posts_url, params=posts_params)
                    print(f"📝 المنشورات - Status: {posts_response.status_code}")
                    print(f"📝 المنشورات - Response: {posts_response.text[:300]}...")
                    
                    if posts_response.status_code == 200:
                        posts_data = posts_response.json()
                        posts = posts_data.get('data', [])
                        print(f"✅ تم جلب {len(posts)} منشور")
                        
                        if posts:
                            # حفظ البيانات للاختبار
                            with open('test_facebook_data.json', 'w', encoding='utf-8') as f:
                                json.dump({
                                    'page_info': pages[0],
                                    'posts': posts
                                }, f, ensure_ascii=False, indent=2)
                            print("✅ تم حفظ البيانات في test_facebook_data.json")
                            return True
                    else:
                        print("❌ فشل في جلب المنشورات")
            else:
                print("❌ فشل في البحث")
        else:
            print(f"❌ فشل في الحصول على Access Token: {response.text}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        import traceback
        traceback.print_exc()
    
    return False

if __name__ == "__main__":
    print("🧪 اختبار شامل لجمع الأخبار")
    print("=" * 50)
    
    # اختبار 1: جمع الأخبار عبر الكلاس
    success1 = test_news_collection()
    
    # اختبار 2: اختبار API مباشرة
    success2 = test_facebook_api_directly()
    
    print("\n" + "=" * 50)
    if success1 or success2:
        print("✅ نجح أحد الاختبارات على الأقل!")
    else:
        print("❌ فشلت جميع الاختبارات")
        print("\n🔧 الحلول المقترحة:")
        print("1. تحقق من صحة App ID و App Secret")
        print("2. تأكد من أن التطبيق نشط في Facebook Developers")
        print("3. تحقق من أن الصفحات المطلوبة عامة")
        print("4. جرب صفحات أخبار مختلفة")
