"""
🔍 فاحص مصادر الفيسبوك
أداة شاملة لفحص إمكانية جلب الأخبار من مصادر الفيسبوك
"""

import requests
import json
import re
from datetime import datetime
from security import security_manager
from inews_collector import iNewsCollector

class FacebookSourcesChecker:
    """فاحص مصادر الفيسبوك"""
    
    def __init__(self):
        self.app_id, self.app_secret = security_manager.get_facebook_credentials()
        self.access_token = None
        self.api_version = "v18.0"
        self.base_url = f"https://graph.facebook.com/{self.api_version}"
        
    def get_access_token(self):
        """الحصول على Access Token"""
        url = f"{self.base_url}/oauth/access_token"
        params = {
            'client_id': self.app_id,
            'client_secret': self.app_secret,
            'grant_type': 'client_credentials'
        }
        
        try:
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get('access_token')
                return True
            else:
                print(f"❌ فشل في الحصول على Access Token: {response.text}")
                return False
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return False
    
    def extract_page_id_from_url(self, url):
        """استخراج Page ID من رابط Facebook"""
        patterns = [
            r'facebook\.com/([^/?]+)',
            r'facebook\.com/pages/[^/]+/(\d+)',
            r'facebook\.com/profile\.php\?id=(\d+)',
            r'^(\d+)$'  # إذا كان Page ID مباشرة
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def check_page_accessibility(self, page_id):
        """فحص إمكانية الوصول للصفحة"""
        if not self.access_token:
            return {"accessible": False, "error": "لا يوجد Access Token"}
        
        # فحص معلومات الصفحة الأساسية
        url = f"{self.base_url}/{page_id}"
        params = {
            'fields': 'id,name,about,category,fan_count,website,verification_status',
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "accessible": True,
                    "page_info": data,
                    "error": None
                }
            else:
                error_data = response.json() if response.text else {}
                return {
                    "accessible": False,
                    "error": error_data.get('error', {}).get('message', 'خطأ غير معروف'),
                    "error_code": error_data.get('error', {}).get('code'),
                    "status_code": response.status_code
                }
        except Exception as e:
            return {
                "accessible": False,
                "error": f"خطأ في الاتصال: {str(e)}"
            }
    
    def check_posts_accessibility(self, page_id):
        """فحص إمكانية الوصول لمنشورات الصفحة"""
        if not self.access_token:
            return {"accessible": False, "error": "لا يوجد Access Token"}
        
        url = f"{self.base_url}/{page_id}/posts"
        params = {
            'fields': 'id,message,story,created_time,type,link',
            'limit': 5,
            'access_token': self.access_token
        }
        
        try:
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                data = response.json()
                posts = data.get('data', [])
                return {
                    "accessible": True,
                    "posts_count": len(posts),
                    "posts_sample": posts[:3],  # عينة من المنشورات
                    "error": None
                }
            else:
                error_data = response.json() if response.text else {}
                return {
                    "accessible": False,
                    "error": error_data.get('error', {}).get('message', 'خطأ غير معروف'),
                    "error_code": error_data.get('error', {}).get('code'),
                    "status_code": response.status_code
                }
        except Exception as e:
            return {
                "accessible": False,
                "error": f"خطأ في الاتصال: {str(e)}"
            }
    
    def check_single_source(self, source_info):
        """فحص مصدر واحد"""
        print(f"\n🔍 فحص المصدر: {source_info.get('name', 'غير محدد')}")
        print(f"📄 الرابط: {source_info.get('url', 'غير محدد')}")
        
        # استخراج Page ID
        page_id = self.extract_page_id_from_url(source_info.get('url', ''))
        if not page_id:
            return {
                "source": source_info,
                "status": "فشل",
                "error": "لا يمكن استخراج Page ID من الرابط",
                "recommendations": ["تحقق من صحة رابط الصفحة"]
            }
        
        print(f"🆔 Page ID: {page_id}")
        
        result = {
            "source": source_info,
            "page_id": page_id,
            "status": "نجح",
            "page_accessible": False,
            "posts_accessible": False,
            "recommendations": []
        }
        
        # فحص الوصول للصفحة
        page_check = self.check_page_accessibility(page_id)
        result["page_check"] = page_check
        
        if page_check["accessible"]:
            print("✅ يمكن الوصول لمعلومات الصفحة")
            result["page_accessible"] = True
            
            page_info = page_check["page_info"]
            print(f"📊 اسم الصفحة: {page_info.get('name', 'غير محدد')}")
            print(f"📊 التصنيف: {page_info.get('category', 'غير محدد')}")
            print(f"📊 المتابعون: {page_info.get('fan_count', 'غير محدد')}")
            
        else:
            print(f"❌ لا يمكن الوصول لمعلومات الصفحة: {page_check['error']}")
            result["status"] = "فشل جزئي"
            
            # تحليل نوع الخطأ وإعطاء توصيات
            error = page_check.get("error", "")
            if "pages_read_engagement" in error:
                result["recommendations"].append("التطبيق يحتاج صلاحية 'pages_read_engagement'")
            elif "Page Public Content Access" in error:
                result["recommendations"].append("التطبيق يحتاج ميزة 'Page Public Content Access'")
            elif "does not exist" in error:
                result["recommendations"].append("الصفحة غير موجودة أو الرابط خاطئ")
            else:
                result["recommendations"].append("تحقق من إعدادات التطبيق في Facebook Developers")
        
        # فحص الوصول للمنشورات
        posts_check = self.check_posts_accessibility(page_id)
        result["posts_check"] = posts_check
        
        if posts_check["accessible"]:
            print(f"✅ يمكن الوصول للمنشورات ({posts_check['posts_count']} منشور)")
            result["posts_accessible"] = True
            
            # عرض عينة من المنشورات
            for i, post in enumerate(posts_check.get('posts_sample', []), 1):
                message = post.get('message', post.get('story', 'بدون نص'))[:50]
                print(f"   {i}. {message}...")
                
        else:
            print(f"❌ لا يمكن الوصول للمنشورات: {posts_check['error']}")
            if result["status"] == "نجح":
                result["status"] = "فشل جزئي"
        
        # تحديد الحالة النهائية
        if result["page_accessible"] and result["posts_accessible"]:
            result["status"] = "نجح كاملاً"
            print("🎉 المصدر يعمل بشكل مثالي!")
        elif result["page_accessible"]:
            result["status"] = "نجح جزئياً"
            print("⚠️ يمكن الوصول للصفحة لكن ليس للمنشورات")
        else:
            result["status"] = "فشل"
            print("❌ لا يمكن الوصول للصفحة أو المنشورات")
        
        return result
    
    def check_all_sources(self):
        """فحص جميع المصادر المضافة"""
        print("🚀 بدء فحص جميع مصادر الفيسبوك")
        print("=" * 60)
        
        # الحصول على Access Token
        if not self.get_access_token():
            print("❌ فشل في الحصول على Access Token")
            return None
        
        print(f"✅ تم الحصول على Access Token: {self.access_token[:30]}...")
        
        # تحميل المصادر
        try:
            with open('pages.json', 'r', encoding='utf-8') as f:
                sources = json.load(f)
        except FileNotFoundError:
            print("❌ لم يتم العثور على ملف المصادر (pages.json)")
            return None
        except Exception as e:
            print(f"❌ خطأ في تحميل المصادر: {e}")
            return None
        
        if not sources:
            print("⚠️ لا توجد مصادر مضافة")
            return []
        
        print(f"📊 عدد المصادر المضافة: {len(sources)}")
        
        # فحص كل مصدر
        results = []
        for i, source in enumerate(sources, 1):
            print(f"\n{'='*20} المصدر {i}/{len(sources)} {'='*20}")
            result = self.check_single_source(source)
            results.append(result)
        
        return results
    
    def generate_report(self, results):
        """إنشاء تقرير شامل"""
        if not results:
            return
        
        print("\n" + "="*60)
        print("📊 تقرير فحص مصادر الفيسبوك")
        print("="*60)
        
        total = len(results)
        working_fully = len([r for r in results if r["status"] == "نجح كاملاً"])
        working_partially = len([r for r in results if r["status"] == "نجح جزئياً"])
        not_working = len([r for r in results if r["status"] == "فشل"])
        
        print(f"📈 إجمالي المصادر: {total}")
        print(f"✅ يعمل بشكل كامل: {working_fully}")
        print(f"⚠️ يعمل جزئياً: {working_partially}")
        print(f"❌ لا يعمل: {not_working}")
        print(f"📊 نسبة النجاح: {((working_fully + working_partially) / total * 100):.1f}%")
        
        # تفاصيل كل مصدر
        print(f"\n📋 تفاصيل المصادر:")
        for i, result in enumerate(results, 1):
            source = result["source"]
            status_icon = {
                "نجح كاملاً": "✅",
                "نجح جزئياً": "⚠️",
                "فشل": "❌"
            }.get(result["status"], "❓")
            
            print(f"\n{i}. {status_icon} {source.get('name', 'غير محدد')}")
            print(f"   الرابط: {source.get('url', 'غير محدد')}")
            print(f"   الحالة: {result['status']}")
            
            if result.get("recommendations"):
                print(f"   التوصيات:")
                for rec in result["recommendations"]:
                    print(f"     • {rec}")
        
        # توصيات عامة
        print(f"\n💡 التوصيات العامة:")
        if not_working > 0:
            print("• تحقق من صحة روابط الصفحات التي لا تعمل")
            print("• تأكد من أن الصفحات عامة وليست محمية")
        
        if working_partially > 0 or not_working > 0:
            print("• قم بطلب صلاحيات إضافية من Facebook:")
            print("  - pages_read_engagement")
            print("  - Page Public Content Access")
            print("• راجع إعدادات التطبيق في Facebook Developers")
        
        if working_fully > 0:
            print(f"• يمكنك جمع الأخبار من {working_fully} مصدر يعمل بشكل كامل")
        
        # حفظ التقرير
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"facebook_sources_report_{timestamp}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "timestamp": datetime.now().isoformat(),
                    "summary": {
                        "total": total,
                        "working_fully": working_fully,
                        "working_partially": working_partially,
                        "not_working": not_working,
                        "success_rate": (working_fully + working_partially) / total * 100
                    },
                    "results": results
                }, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 تم حفظ التقرير في: {report_file}")
            
        except Exception as e:
            print(f"❌ فشل في حفظ التقرير: {e}")

def main():
    """تشغيل فاحص المصادر"""
    checker = FacebookSourcesChecker()
    results = checker.check_all_sources()
    
    if results is not None:
        checker.generate_report(results)
    
    print(f"\n🏁 انتهى فحص المصادر")

if __name__ == "__main__":
    main()
