#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة للحصول على User Access Token من Facebook
"""

import os
import webbrowser

def open_graph_api_explorer():
    """فتح Graph API Explorer في المتصفح"""
    print("🚀 فتح Graph API Explorer...")
    
    # رابط Graph API Explorer مع التطبيق المحدد
    app_id = "1905779223574690"
    url = f"https://developers.facebook.com/tools/explorer/{app_id}/"
    
    try:
        webbrowser.open(url)
        print(f"✅ تم فتح الرابط في المتصفح: {url}")
        return True
    except Exception as e:
        print(f"❌ فشل في فتح المتصفح: {e}")
        print(f"🔗 افتح هذا الرابط يدوياً: {url}")
        return False

def show_instructions():
    """عرض التعليمات التفصيلية"""
    print("📋 تعليمات الحصول على User Access Token")
    print("=" * 60)
    
    print("\n🎯 الهدف:")
    print("الحصول على User Access Token للتغلب على قيود Facebook API")
    
    print("\n📝 الخطوات:")
    print("1. ✅ تم فتح Graph API Explorer في المتصفح")
    print("2. 🔧 في القائمة العلوية، تأكد من اختيار تطبيق 'inews'")
    print("3. 🔑 اضغط على 'Add a Permission' وأضف:")
    print("   • public_profile")
    print("   • pages_show_list")
    print("   • pages_read_engagement (إذا كان متاح)")
    print("4. 🎫 اضغط 'Generate Access Token'")
    print("5. 📱 سجل الدخول لـ Facebook ووافق على الصلاحيات")
    print("6. 📋 انسخ الـ Access Token (يبدأ بـ EAAB...)")
    
    print("\n⚠️ ملاحظات مهمة:")
    print("• الـ Token ينتهي خلال ساعات (مؤقت)")
    print("• مرتبط بحسابك الشخصي")
    print("• يحتاج تجديد دوري")

def add_token_to_env():
    """مساعدة في إضافة الـ Token لملف .env"""
    print("\n" + "=" * 60)
    print("📁 إضافة الـ Token لملف .env")
    print("=" * 60)
    
    token = input("\n🔑 الصق الـ Access Token هنا (أو اضغط Enter للتخطي): ").strip()
    
    if not token:
        print("⏭️ تم التخطي. يمكنك إضافة الـ Token يدوياً لاحقاً.")
        return False
    
    if not token.startswith('EAAB'):
        print("⚠️ تحذير: الـ Token لا يبدأ بـ EAAB، تأكد من صحته")
    
    try:
        # قراءة ملف .env الحالي
        env_content = ""
        env_file = ".env"
        
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                env_content = f.read()
        
        # إضافة أو تحديث FACEBOOK_USER_ACCESS_TOKEN
        lines = env_content.split('\n')
        token_line = f"FACEBOOK_USER_ACCESS_TOKEN={token}"
        
        # البحث عن السطر الموجود
        found = False
        for i, line in enumerate(lines):
            if line.startswith('FACEBOOK_USER_ACCESS_TOKEN='):
                lines[i] = token_line
                found = True
                break
        
        # إضافة السطر إذا لم يكن موجود
        if not found:
            lines.append(token_line)
        
        # حفظ الملف
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print(f"✅ تم حفظ الـ Token في ملف .env")
        print(f"📄 الملف: {os.path.abspath(env_file)}")
        return True
        
    except Exception as e:
        print(f"❌ فشل في حفظ الـ Token: {e}")
        print(f"💡 أضف هذا السطر يدوياً لملف .env:")
        print(f"FACEBOOK_USER_ACCESS_TOKEN={token}")
        return False

def test_token():
    """اختبار الـ Token الجديد"""
    print("\n" + "=" * 60)
    print("🧪 اختبار الـ Token الجديد")
    print("=" * 60)
    
    try:
        from inews_collector import iNewsCollector
        
        print("🔄 إنشاء جامع الأخبار...")
        collector = iNewsCollector()
        
        print("🔑 اختبار الـ Access Token...")
        if collector.get_access_token():
            print("✅ تم الحصول على Access Token بنجاح!")
            
            # اختبار بسيط
            print("🧪 اختبار الوصول لـ Facebook API...")
            try:
                import requests
                url = f"https://graph.facebook.com/v18.0/me"
                params = {'access_token': collector.access_token}
                
                response = requests.get(url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ الـ Token يعمل! مرحباً {data.get('name', 'مستخدم')}")
                    return True
                else:
                    print(f"❌ خطأ في الـ Token: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ خطأ في الاختبار: {e}")
                return False
        else:
            print("❌ فشل في الحصول على Access Token")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الـ Token: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔑 أداة الحصول على Facebook User Access Token")
    print("=" * 60)
    print("🎯 الهدف: حل مشكلة صلاحيات Facebook API")
    print("⏰ الوقت المطلوب: 5 دقائق")
    print("=" * 60)
    
    # فتح Graph API Explorer
    if open_graph_api_explorer():
        print("\n✅ تم فتح Graph API Explorer في المتصفح")
    else:
        print("\n❌ فشل في فتح المتصفح")
    
    # عرض التعليمات
    show_instructions()
    
    # انتظار المستخدم
    input("\n⏸️ اضغط Enter بعد الحصول على الـ Access Token...")
    
    # إضافة الـ Token
    if add_token_to_env():
        print("\n✅ تم حفظ الـ Token بنجاح!")
        
        # اختبار الـ Token
        if test_token():
            print("\n🎉 تم! الـ Token يعمل بمثالية!")
            print("🚀 يمكنك الآن استخدام التطبيق لجمع الأخبار")
        else:
            print("\n❌ هناك مشكلة في الـ Token")
            print("💡 جرب الحصول على Token جديد")
    else:
        print("\n⚠️ لم يتم حفظ الـ Token")
        print("💡 أضفه يدوياً لملف .env")
    
    print("\n📋 الخطوات التالية:")
    print("1. 🔄 أعد تشغيل التطبيق")
    print("2. 🎯 جرب جمع الأخبار من لوحة التحكم")
    print("3. 🎊 استمتع بالأخبار!")

if __name__ == "__main__":
    main()
