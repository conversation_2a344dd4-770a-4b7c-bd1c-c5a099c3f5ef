
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-05-16T12:15:52-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "0c8d73757f54788d5fa213a678dfbdf3fdd7ccfb",
 "version": "5.2.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
