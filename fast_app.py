#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق سريع لجمع الأخبار من تلغرام
"""

import asyncio
import os
import json
from datetime import datetime, timedelta
import threading
import time

try:
    from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
    from telethon import TelegramClient
except ImportError:
    print("تثبيت المكتبات...")
    os.system("pip install flask telethon python-dotenv")
    from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
    from telethon import TelegramClient

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = 'TelegramNews_Ultra_Secure_Key_2024_Yaser_Protection'

# بيانات تلغرام
API_ID = "21311521"
API_HASH = "7e63557fb0d1324435d6c055d363d8f2"
PHONE = "+9647714366758"

# متغيرات عامة
collection_status = {
    'is_running': False,
    'last_update': None,
    'total_news': 0,
    'channels_count': 5,
    'error_message': None
}

# أخبار تجريبية سريعة
latest_news = [
    {
        'channel_name': 'قناة الجزيرة',
        'channel_username': 'AjaNews',
        'text': 'عاجل: تطورات جديدة في الأوضاع السياسية بالمنطقة',
        'date': datetime.now().isoformat(),
        'message_id': 12345,
        'views': 15420,
        'forwards': 234,
        'has_media': False,
        'media_type': None
    },
    {
        'channel_name': 'صابرين نيوز',
        'channel_username': 'SabrenNews22',
        'text': 'أخبار اقتصادية: ارتفاع أسعار النفط في الأسواق العالمية',
        'date': (datetime.now() - timedelta(hours=1)).isoformat(),
        'message_id': 12346,
        'views': 8930,
        'forwards': 156,
        'has_media': True,
        'media_type': 'photo'
    },
    {
        'channel_name': 'Shafaq news',
        'channel_username': 'shafaaqnews',
        'text': 'تقرير: الأحوال الجوية المتوقعة للأيام القادمة',
        'date': (datetime.now() - timedelta(hours=2)).isoformat(),
        'message_id': 12347,
        'views': 5670,
        'forwards': 89,
        'has_media': False,
        'media_type': None
    },
    {
        'channel_name': 'الفرات نيوز',
        'channel_username': 'alforat_tv2',
        'text': 'عاجل: اجتماع مهم لمجلس الوزراء لمناقشة القضايا الراهنة',
        'date': (datetime.now() - timedelta(hours=3)).isoformat(),
        'message_id': 12348,
        'views': 12340,
        'forwards': 201,
        'has_media': True,
        'media_type': 'video'
    },
    {
        'channel_name': 'الاعظمية نيوز',
        'channel_username': 'adh_news',
        'text': 'تطورات ميدانية: آخر المستجدات من العاصمة بغداد',
        'date': (datetime.now() - timedelta(hours=4)).isoformat(),
        'message_id': 12349,
        'views': 7890,
        'forwards': 123,
        'has_media': False,
        'media_type': None
    }
]

channels_list = [
    {'name': 'قناة الجزيرة', 'username': 'AjaNews', 'participants_count': 1405216, 'is_news_channel': True},
    {'name': 'صابرين نيوز', 'username': 'SabrenNews22', 'participants_count': 571597, 'is_news_channel': True},
    {'name': 'الاعظمية نيوز', 'username': 'adh_news', 'participants_count': 47380, 'is_news_channel': True},
    {'name': 'الفرات نيوز', 'username': 'alforat_tv2', 'participants_count': 20278, 'is_news_channel': True},
    {'name': 'Shafaq news', 'username': 'shafaaqnews', 'participants_count': 9227, 'is_news_channel': True}
]

def check_admin_password(password):
    """التحقق من كلمة مرور الإدارة"""
    return password == 'yaseraljebori@25m'

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html', 
                         collection_status=collection_status,
                         latest_news=latest_news[:10])

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        password = request.form.get('password')
        if check_admin_password(password):
            session['logged_in'] = True
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.pop('logged_in', None)
    flash('تم تسجيل الخروج', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))
    
    return render_template('dashboard.html',
                         collection_status=collection_status,
                         channels_list=channels_list,
                         latest_news=latest_news[:20])

@app.route('/api/start_collection', methods=['POST'])
def start_collection():
    """بدء جمع الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    if collection_status['is_running']:
        return jsonify({'error': 'جمع الأخبار يعمل بالفعل'}), 400
    
    try:
        # محاكاة جمع سريع
        thread = threading.Thread(target=quick_news_collection)
        thread.daemon = True
        thread.start()
        
        return jsonify({'message': 'تم بدء جمع الأخبار'})
    
    except Exception as e:
        return jsonify({'error': f'فشل في بدء جمع الأخبار: {str(e)}'}), 500

@app.route('/api/stop_collection', methods=['POST'])
def stop_collection():
    """إيقاف جمع الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    collection_status['is_running'] = False
    return jsonify({'message': 'تم إيقاف جمع الأخبار'})

@app.route('/api/status')
def get_status():
    """الحصول على حالة جمع الأخبار"""
    return jsonify(collection_status)

@app.route('/news')
def news_page():
    """صفحة الأخبار"""
    return render_template('news.html', latest_news=latest_news)

@app.route('/setup')
def setup():
    """صفحة الإعداد الأولي"""
    return render_template('setup.html')

@app.route('/api/test_connection', methods=['POST'])
def test_connection():
    """اختبار الاتصال بتلغرام"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    # محاكاة اختبار سريع
    return jsonify({'message': 'تم الاتصال بتلغرام بنجاح! (محاكاة سريعة)'})

def quick_news_collection():
    """جمع أخبار سريع (محاكاة)"""
    global collection_status, latest_news
    
    try:
        collection_status['is_running'] = True
        collection_status['error_message'] = None
        
        print("🚀 بدء جمع الأخبار السريع...")
        
        # محاكاة جمع الأخبار
        for i in range(5):
            time.sleep(1)  # انتظار ثانية واحدة
            print(f"📰 جمع أخبار من القناة {i+1}/5...")
        
        # إضافة أخبار جديدة
        new_news = [
            {
                'channel_name': 'قناة الجزيرة',
                'channel_username': 'AjaNews',
                'text': f'خبر عاجل جديد - {datetime.now().strftime("%H:%M")}',
                'date': datetime.now().isoformat(),
                'message_id': 50000 + len(latest_news),
                'views': 1000,
                'forwards': 50,
                'has_media': False,
                'media_type': None
            }
        ]
        
        latest_news = new_news + latest_news
        
        # تحديث الإحصائيات
        collection_status['total_news'] = len(latest_news)
        collection_status['channels_count'] = 5
        collection_status['last_update'] = datetime.now().isoformat()
        
        print(f"✅ تم جمع {len(latest_news)} خبر بنجاح!")
        
    except Exception as e:
        collection_status['error_message'] = str(e)
        print(f"❌ خطأ في جمع الأخبار: {e}")
    
    finally:
        collection_status['is_running'] = False

def real_telegram_collection():
    """جمع حقيقي من تلغرام (للاستخدام لاحقاً)"""
    global collection_status, latest_news, channels_list
    
    try:
        collection_status['is_running'] = True
        collection_status['error_message'] = None
        
        # إنشاء حلقة أحداث جديدة للخيط
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # تشغيل جمع الأخبار
        loop.run_until_complete(collect_real_news())
        
    except Exception as e:
        collection_status['error_message'] = str(e)
        print(f"❌ خطأ في جمع الأخبار: {e}")
    
    finally:
        collection_status['is_running'] = False

async def collect_real_news():
    """جمع الأخبار الحقيقي من تلغرام"""
    global collection_status, latest_news, channels_list
    
    client = TelegramClient('fast_session', API_ID, API_HASH)
    
    try:
        print("🔄 بدء الاتصال بتلغرام...")
        await client.start(phone=PHONE)
        print("✅ تم الاتصال بتلغرام")
        
        # جمع أخبار سريع من 3 قنوات فقط
        target_channels = ['@AjaNews', '@SabrenNews22', '@shafaaqnews']
        all_news = []
        
        for channel_username in target_channels:
            try:
                print(f"📰 جمع أخبار من: {channel_username}")
                
                async for message in client.iter_messages(channel_username, limit=5):
                    if message.text and len(message.text) > 30:
                        news_item = {
                            'channel_name': channel_username.replace('@', ''),
                            'channel_username': channel_username.replace('@', ''),
                            'text': message.text[:200] + "..." if len(message.text) > 200 else message.text,
                            'date': message.date.isoformat(),
                            'message_id': message.id,
                            'views': getattr(message, 'views', 0),
                            'forwards': getattr(message, 'forwards', 0),
                            'has_media': bool(message.media),
                            'media_type': type(message.media).__name__ if message.media else None
                        }
                        all_news.append(news_item)
                
                print(f"   ✅ تم جمع الأخبار")
                
            except Exception as e:
                print(f"   ❌ خطأ في {channel_username}: {e}")
        
        # تحديث البيانات
        if all_news:
            latest_news = all_news
            collection_status['total_news'] = len(all_news)
            collection_status['last_update'] = datetime.now().isoformat()
            print(f"🎉 تم جمع {len(all_news)} خبر!")
        
    except Exception as e:
        print(f"❌ خطأ في جمع الأخبار: {e}")
        raise e
    
    finally:
        await client.disconnect()
        print("✅ تم إغلاق الاتصال بتلغرام")

if __name__ == '__main__':
    print("🚀 تطبيق جمع الأخبار السريع من تلغرام")
    print("=" * 60)
    print("🌐 الواجهة متاحة على: http://localhost:5020")
    print("🔑 كلمة مرور الإدارة: yaseraljebori@25m")
    print("📞 رقم الهاتف: +9647714366758")
    print("⚡ نسخة سريعة - جمع فوري!")
    print("=" * 60)
    
    # تحديث الإحصائيات الأولية
    collection_status['total_news'] = len(latest_news)
    collection_status['last_update'] = datetime.now().isoformat()
    
    # تشغيل التطبيق
    app.run(host='0.0.0.0', port=5021, debug=True)
