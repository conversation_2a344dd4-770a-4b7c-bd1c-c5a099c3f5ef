{% extends "base.html" %}

{% block title %}إدارة المصادر - iNews{% endblock %}

{% block content %}
<div class="page-header text-center">
    <h1 class="display-4 fw-bold mb-3">
        <i class="fas fa-cogs me-3"></i>
        إدارة المصادر
    </h1>
    <p class="lead">إضافة وإدارة مصادر الأخبار من الفيسبوك</p>
</div>

<!-- إضافة مصدر جديد -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-plus me-2"></i>
            إضافة مصدر جديد
        </h5>
    </div>
    <div class="card-body">
        <form id="add-page-form">
            <div class="row">
                <div class="col-md-6">
                    <label for="page-url" class="form-label">رابط صفحة الفيسبوك *</label>
                    <input type="url" class="form-control" id="page-url" required 
                           placeholder="https://facebook.com/page-name">
                    <div class="form-text">
                        مثال: https://facebook.com/BBCArabic أو facebook.com/aljazeera
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="page-name" class="form-label">اسم المصدر</label>
                    <input type="text" class="form-control" id="page-name" 
                           placeholder="اسم المصدر (اختياري)">
                </div>
                <div class="col-md-3">
                    <label for="page-category" class="form-label">التصنيف</label>
                    <select class="form-select" id="page-category">
                        <option value="أخبار عامة">أخبار عامة</option>
                        <option value="أخبار محلية">أخبار محلية</option>
                        <option value="أخبار عالمية">أخبار عالمية</option>
                        <option value="رياضة">رياضة</option>
                        <option value="تقنية">تقنية</option>
                        <option value="اقتصاد">اقتصاد</option>
                        <option value="صحة">صحة</option>
                        <option value="ثقافة">ثقافة</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة المصدر
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جمع الأخبار -->
<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-download me-2"></i>
            جمع الأخبار
        </h5>
    </div>
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <label for="posts-per-page" class="form-label">عدد المنشورات لكل صفحة</label>
                <select class="form-select" id="posts-per-page">
                    <option value="3">3 منشورات</option>
                    <option value="5" selected>5 منشورات</option>
                    <option value="10">10 منشورات</option>
                    <option value="15">15 منشور</option>
                    <option value="20">20 منشور</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button class="btn btn-success" onclick="collectNews()">
                        <i class="fas fa-sync-alt me-2"></i>
                        جمع الأخبار الآن
                    </button>
                    <button class="btn btn-info ms-2" onclick="testConnection()">
                        <i class="fas fa-wifi me-2"></i>
                        اختبار الاتصال
                    </button>
                </div>
            </div>
        </div>
        
        <!-- شريط التقدم -->
        <div id="progress-container" style="display: none;" class="mt-3">
            <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted mt-1 d-block" id="progress-text">جاري جمع الأخبار...</small>
        </div>
    </div>
</div>

<!-- قائمة المصادر -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            المصادر المضافة ({{ pages|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if pages %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>المصدر</th>
                        <th>التصنيف</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="pages-table">
                    {% for page in pages %}
                    <tr data-page-id="{{ page.id }}">
                        <td>
                            <div>
                                <strong>{{ page.name }}</strong>
                                <br>
                                <small class="text-muted">
                                    <i class="fab fa-facebook me-1"></i>
                                    {{ page.url }}
                                </small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ page.category }}</span>
                        </td>
                        <td>
                            {% if page.active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-warning">معطل</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ page.added_at[:10] if page.added_at else 'غير محدد' }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" 
                                        onclick="togglePage('{{ page.id }}')">
                                    <i class="fas fa-{{ 'pause' if page.active else 'play' }}"></i>
                                </button>
                                <button class="btn btn-outline-danger" 
                                        onclick="removePage('{{ page.id }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مصادر مضافة</h5>
            <p class="text-muted">ابدأ بإضافة مصادر الأخبار من الفيسبوك</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إضافة مصدر جديد
$('#add-page-form').on('submit', function(e) {
    e.preventDefault();
    
    const url = $('#page-url').val().trim();
    const name = $('#page-name').val().trim();
    const category = $('#page-category').val();
    
    if (!url) {
        showAlert('يرجى إدخال رابط الصفحة', 'warning');
        return;
    }
    
    $.ajax({
        url: '/add_page',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            url: url,
            name: name,
            category: category
        }),
        success: function(response) {
            if (response.success) {
                showAlert('تم إضافة المصدر بنجاح', 'success');
                location.reload();
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    });
});

// تفعيل/إلغاء تفعيل مصدر
function togglePage(pageId) {
    $.ajax({
        url: '/toggle_page',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({page_id: pageId}),
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                location.reload();
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    });
}

// حذف مصدر
function removePage(pageId) {
    if (!confirm('هل أنت متأكد من حذف هذا المصدر؟')) {
        return;
    }
    
    $.ajax({
        url: '/remove_page',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({page_id: pageId}),
        success: function(response) {
            if (response.success) {
                showAlert(response.message, 'success');
                $(`tr[data-page-id="${pageId}"]`).fadeOut();
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    });
}

// جمع الأخبار
function collectNews() {
    const postsPerPage = $('#posts-per-page').val();
    
    $('#progress-container').show();
    $('.progress-bar').css('width', '0%');
    $('#progress-text').text('بدء جمع الأخبار...');
    
    // محاكاة شريط التقدم
    let progress = 0;
    const progressInterval = setInterval(function() {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        $('.progress-bar').css('width', progress + '%');
    }, 500);
    
    $.ajax({
        url: '/collect_news',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({posts_per_page: parseInt(postsPerPage)}),
        success: function(response) {
            clearInterval(progressInterval);
            $('.progress-bar').css('width', '100%');
            
            if (response.success) {
                $('#progress-text').text(`تم جمع ${response.news_count} خبر بنجاح!`);
                showAlert(`تم جمع ${response.news_count} خبر بنجاح`, 'success');
                
                setTimeout(function() {
                    $('#progress-container').hide();
                }, 3000);
            } else {
                $('#progress-text').text('فشل في جمع الأخبار');
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            clearInterval(progressInterval);
            $('#progress-text').text('خطأ في الاتصال');
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        }
    });
}

// اختبار الاتصال
function testConnection() {
    $.get('/test_connection')
        .done(function(response) {
            if (response.success) {
                showAlert('الاتصال مع Facebook API يعمل بشكل صحيح', 'success');
            } else {
                showAlert('فشل الاتصال: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

// عرض التنبيهات
function showAlert(message, type) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alert);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
