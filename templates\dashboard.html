{% extends "base.html" %}

{% block title %}لوحة التحكم - جامع الأخبار من تلغرام{% endblock %}

{% block content %}
<div class="row">
    <!-- عنوان لوحة التحكم -->
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="bi bi-speedometer2 text-primary"></i>
                    لوحة التحكم
                </h2>
                <p class="card-text text-muted">
                    إدارة ومراقبة عملية جمع الأخبار من تلغرام
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- أزرار التحكم -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear"></i>
                    التحكم في الجمع
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button onclick="startCollection()" class="btn btn-success">
                        <i class="bi bi-play-circle"></i>
                        بدء جمع الأخبار
                    </button>
                    
                    <button onclick="stopCollection()" class="btn btn-danger">
                        <i class="bi bi-stop-circle"></i>
                        إيقاف الجمع
                    </button>
                    
                    <button onclick="testConnection()" class="btn btn-info">
                        <i class="bi bi-wifi"></i>
                        اختبار الاتصال
                    </button>
                </div>
            </div>
        </div>
        
        <!-- تصدير البيانات -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-download"></i>
                    تصدير البيانات
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button onclick="exportNews('csv')" class="btn btn-outline-primary">
                        <i class="bi bi-file-earmark-spreadsheet"></i>
                        تصدير CSV
                    </button>
                    
                    <button onclick="exportNews('json')" class="btn btn-outline-secondary">
                        <i class="bi bi-file-earmark-code"></i>
                        تصدير JSON
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الإحصائيات -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    الإحصائيات
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="p-3">
                            <i class="bi bi-newspaper display-6 text-primary"></i>
                            <h4 id="total-news" class="mt-2">{{ collection_status.total_news or 0 }}</h4>
                            <p class="text-muted">إجمالي الأخبار</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="p-3">
                            <i class="bi bi-broadcast display-6 text-success"></i>
                            <h4 id="channels-count">{{ collection_status.channels_count or 0 }}</h4>
                            <p class="text-muted">عدد القنوات</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="p-3">
                            <i class="bi bi-clock display-6 text-info"></i>
                            <h4>24</h4>
                            <p class="text-muted">ساعة (فترة الجمع)</p>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="p-3">
                            <i class="bi bi-activity display-6 text-warning"></i>
                            <div id="collection-status">
                                {% if collection_status.is_running %}
                                    <span class="status-badge bg-success">نشط</span>
                                {% else %}
                                    <span class="status-badge bg-secondary">متوقف</span>
                                {% endif %}
                            </div>
                            <p class="text-muted">حالة النظام</p>
                        </div>
                    </div>
                </div>
                
                {% if collection_status.last_update %}
                <div class="text-center mt-3">
                    <small class="text-muted">
                        آخر تحديث: {{ collection_status.last_update[:19] }}
                    </small>
                </div>
                {% endif %}
                
                {% if collection_status.error_message %}
                <div class="alert alert-danger mt-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ collection_status.error_message }}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- قائمة القنوات -->
{% if channels_list %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i>
                    القنوات المشترك فيها
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم القناة</th>
                                <th>اسم المستخدم</th>
                                <th>عدد المشتركين</th>
                                <th>نوع القناة</th>
                                <th>آخر رسالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for channel in channels_list[:20] %}
                            <tr>
                                <td>
                                    <strong>{{ channel.name }}</strong>
                                    {% if channel.is_news_channel %}
                                        <span class="badge bg-primary ms-2">إخبارية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if channel.username %}
                                        <code>@{{ channel.username }}</code>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if channel.participants_count %}
                                        {{ "{:,}".format(channel.participants_count) }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if channel.is_news_channel %}
                                        <span class="badge bg-success">أخبار</span>
                                    {% else %}
                                        <span class="badge bg-secondary">عامة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if channel.last_message_date %}
                                        <small>{{ channel.last_message_date.strftime('%Y-%m-%d') }}</small>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if channels_list|length > 20 %}
                <div class="text-center mt-3">
                    <small class="text-muted">
                        عرض 20 من أصل {{ channels_list|length }} قناة
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- آخر الأخبار -->
{% if latest_news %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    آخر الأخبار المجمعة
                </h5>
                <a href="{{ url_for('news_page') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% for news in latest_news %}
                <div class="news-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <span class="channel-tag">{{ news.channel_name }}</span>
                        <small class="text-muted">
                            {% if news.date %}
                                {{ news.date.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </small>
                    </div>
                    
                    <p class="mb-2">
                        {% if news.text|length > 150 %}
                            {{ news.text[:150] }}...
                        {% else %}
                            {{ news.text }}
                        {% endif %}
                    </p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if news.views %}
                                <small class="text-muted me-3">
                                    <i class="bi bi-eye"></i> {{ news.views }}
                                </small>
                            {% endif %}
                            {% if news.forwards %}
                                <small class="text-muted me-3">
                                    <i class="bi bi-share"></i> {{ news.forwards }}
                                </small>
                            {% endif %}
                        </div>
                        
                        {% if news.has_media %}
                            <span class="badge bg-secondary">
                                <i class="bi bi-image"></i> وسائط
                            </span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function testConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الاختبار...';
    btn.disabled = true;
    
    fetch('/api/test_connection', {method: 'POST'})
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('خطأ في الاتصال: ' + data.error);
            } else {
                alert('تم الاتصال بتلغرام بنجاح!');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في اختبار الاتصال');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}
</script>
{% endblock %}
