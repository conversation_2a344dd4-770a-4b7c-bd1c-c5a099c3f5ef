#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل متقدم لمشكلة صلاحيات Facebook API
"""

import webbrowser
import requests
import os
from dotenv import load_dotenv

load_dotenv()

def check_app_permissions():
    """فحص صلاحيات التطبيق المتاحة"""
    print("🔍 فحص صلاحيات التطبيق...")
    print("=" * 50)
    
    app_id = os.getenv('FACEBOOK_APP_ID')
    app_secret = os.getenv('FACEBOOK_APP_SECRET')
    
    if not app_id or not app_secret:
        print("❌ بيانات التطبيق مفقودة في .env")
        return False
    
    try:
        # الحصول على App Access Token
        url = "https://graph.facebook.com/oauth/access_token"
        params = {
            'client_id': app_id,
            'client_secret': app_secret,
            'grant_type': 'client_credentials'
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            app_token = data.get('access_token')
            
            print(f"✅ App Access Token: {app_token[:20]}...")
            
            # فحص صلاحيات التطبيق
            perms_url = f"https://graph.facebook.com/v18.0/{app_id}/permissions"
            perms_params = {'access_token': app_token}
            
            perms_response = requests.get(perms_url, params=perms_params, timeout=10)
            
            if perms_response.status_code == 200:
                perms_data = perms_response.json()
                permissions = perms_data.get('data', [])
                
                print(f"📋 صلاحيات التطبيق المتاحة:")
                for perm in permissions:
                    print(f"   • {perm.get('permission', 'unknown')}")
                
                return True
            else:
                print(f"❌ فشل في جلب صلاحيات التطبيق")
                return False
                
        else:
            print(f"❌ فشل في الحصول على App Token: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def try_alternative_permissions():
    """جرب صلاحيات بديلة أقل تقييداً"""
    print("\n🔄 جرب صلاحيات بديلة...")
    print("=" * 50)
    
    app_id = os.getenv('FACEBOOK_APP_ID')
    
    # صلاحيات بديلة أقل تقييداً
    alternative_permissions = [
        'public_profile',
        'email',
        'user_friends',
        'user_gender',
        'user_age_range'
    ]
    
    # بناء رابط مع الصلاحيات البديلة
    permissions_str = ','.join(alternative_permissions)
    
    # رابط Facebook Login مباشر
    redirect_uri = "https://developers.facebook.com/tools/explorer/"
    
    login_url = f"https://www.facebook.com/v18.0/dialog/oauth?" \
                f"client_id={app_id}&" \
                f"redirect_uri={redirect_uri}&" \
                f"scope={permissions_str}&" \
                f"response_type=token"
    
    print(f"🔗 رابط تسجيل الدخول المباشر:")
    print(f"{login_url}")
    
    try:
        webbrowser.open(login_url)
        print(f"✅ تم فتح رابط تسجيل الدخول")
        
        print(f"\n📋 الخطوات:")
        print(f"1. سجل الدخول بحساب Facebook")
        print(f"2. وافق على الصلاحيات المطلوبة")
        print(f"3. ستحصل على Access Token في الرابط")
        print(f"4. انسخ الـ Token من الرابط (بعد access_token=)")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في فتح الرابط: {e}")
        return False

def try_graph_explorer_different_way():
    """جرب Graph API Explorer بطريقة مختلفة"""
    print("\n🔧 Graph API Explorer - طريقة مختلفة...")
    print("=" * 50)
    
    app_id = os.getenv('FACEBOOK_APP_ID')
    
    # رابط مباشر لـ Graph API Explorer مع إعدادات محددة
    explorer_url = f"https://developers.facebook.com/tools/explorer/{app_id}/" \
                   f"?method=GET&path=me&version=v18.0"
    
    try:
        webbrowser.open(explorer_url)
        print(f"✅ تم فتح Graph API Explorer")
        
        print(f"\n📋 جرب هذه الطريقة:")
        print(f"1. في Graph API Explorer، اضغط 'Get Token'")
        print(f"2. اختر 'Get User Access Token'")
        print(f"3. ستظهر نافذة الصلاحيات - اختر ما تستطيع:")
        print(f"   • user_posts (إذا ظهرت)")
        print(f"   • user_likes (إذا ظهرت)")
        print(f"   • pages_show_list (إذا ظهرت)")
        print(f"   • أي صلاحيات أخرى متاحة")
        print(f"4. اضغط 'Generate Access Token'")
        print(f"5. انسخ الـ Token الجديد")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل: {e}")
        return False

def suggest_page_solution():
    """اقتراح حل باستخدام صفحة Facebook"""
    print("\n💡 الحل البديل: استخدام صفحة Facebook")
    print("=" * 50)
    
    print(f"🎯 إذا لم تنجح الطرق السابقة:")
    print(f"")
    print(f"1️⃣ **أنشئ صفحة Facebook:**")
    print(f"   • اذهب لـ: https://www.facebook.com/pages/create")
    print(f"   • أنشئ صفحة باسم 'أخبار ياسر' أو أي اسم")
    print(f"   • اجعلها صفحة 'إعلام/أخبار'")
    print(f"")
    print(f"2️⃣ **اربط الصفحة بالتطبيق:**")
    print(f"   • في Facebook Developers Console")
    print(f"   • اذهب لـ 'Roles' > 'Pages'")
    print(f"   • أضف صفحتك للتطبيق")
    print(f"")
    print(f"3️⃣ **احصل على Page Access Token:**")
    print(f"   • في Graph API Explorer")
    print(f"   • اختر 'Get Page Access Token'")
    print(f"   • اختر صفحتك")
    print(f"   • احصل على Token للصفحة")
    print(f"")
    print(f"4️⃣ **استخدم Page Token:**")
    print(f"   • Page Token له صلاحيات أكثر")
    print(f"   • يمكن قراءة منشورات الصفحات الأخرى")
    print(f"   • أقل قيود من User Token")

def test_current_capabilities():
    """اختبار القدرات الحالية مع الصلاحيات المتاحة"""
    print("\n🧪 اختبار القدرات الحالية...")
    print("=" * 50)
    
    token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    
    if not token:
        print("❌ لا يوجد User Token")
        return False
    
    # اختبارات مع الصلاحيات المتاحة فقط
    tests = [
        {
            'name': 'معلومات المستخدم الأساسية',
            'url': 'https://graph.facebook.com/v18.0/me',
            'params': {'fields': 'id,name,email,picture'}
        },
        {
            'name': 'الأصدقاء (إذا متاح)',
            'url': 'https://graph.facebook.com/v18.0/me/friends',
            'params': {'limit': 5}
        },
        {
            'name': 'الصور (إذا متاح)',
            'url': 'https://graph.facebook.com/v18.0/me/photos',
            'params': {'limit': 3}
        }
    ]
    
    working_features = []
    
    for test in tests:
        print(f"\n🔍 {test['name']}:")
        
        try:
            params = {'access_token': token}
            params.update(test['params'])
            
            response = requests.get(test['url'], params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data:
                    items = data['data']
                    print(f"   ✅ يعمل! عدد العناصر: {len(items)}")
                    working_features.append(test['name'])
                else:
                    print(f"   ✅ يعمل! البيانات متوفرة")
                    working_features.append(test['name'])
                    
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"   ❌ لا يعمل: {error_msg}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
    
    print(f"\n📊 الميزات التي تعمل: {len(working_features)}")
    
    if working_features:
        print(f"✅ يمكن استخدام التطبيق للميزات المتاحة")
        return True
    else:
        print(f"❌ نحتاج صلاحيات إضافية")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 حل متقدم لمشكلة صلاحيات Facebook API")
    print("=" * 60)
    print("🎯 الهدف: الحصول على صلاحيات أكثر أو إيجاد بدائل")
    print("=" * 60)
    
    # فحص صلاحيات التطبيق
    check_app_permissions()
    
    # اختبار القدرات الحالية
    current_works = test_current_capabilities()
    
    if not current_works:
        print(f"\n🔄 جرب الحلول التالية:")
        
        # الحل 1: صلاحيات بديلة
        print(f"\n" + "="*50)
        print(f"🔄 الحل 1: صلاحيات بديلة")
        try_alternative_permissions()
        
        # الحل 2: Graph Explorer بطريقة مختلفة
        print(f"\n" + "="*50)
        print(f"🔄 الحل 2: Graph API Explorer مختلف")
        try_graph_explorer_different_way()
        
        # الحل 3: استخدام صفحة
        print(f"\n" + "="*50)
        suggest_page_solution()
    
    print(f"\n📋 الخطوات التالية:")
    print(f"1. جرب أحد الحلول أعلاه")
    print(f"2. احصل على Token جديد مع صلاحيات أكثر")
    print(f"3. شغل get_facebook_token.py مرة أخرى")
    print(f"4. أو جرب التطبيق بالصلاحيات المتاحة")

if __name__ == "__main__":
    main()
