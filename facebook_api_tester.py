#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة اختبار وتشخيص Facebook API
لمساعدة في حل مشاكل الصلاحيات والوصول
"""

import requests
import json
from datetime import datetime
from security import security_manager

class FacebookAPITester:
    """أداة اختبار Facebook API"""
    
    def __init__(self):
        # الحصول على بيانات Facebook من الإعدادات الآمنة
        self.app_id, self.app_secret = security_manager.get_facebook_credentials()
        self.base_url = "https://graph.facebook.com/v18.0"
        self.access_token = None
    
    def test_app_credentials(self):
        """اختبار صحة بيانات التطبيق"""
        print("🔐 اختبار بيانات التطبيق...")
        print("=" * 50)
        
        if not self.app_id or not self.app_secret:
            print("❌ بيانات التطبيق مفقودة في ملف .env")
            print("💡 تأكد من وجود FACEBOOK_APP_ID و FACEBOOK_APP_SECRET")
            return False
        
        print(f"✅ App ID: {self.app_id}")
        print(f"✅ App Secret: {'*' * len(self.app_secret)}")
        return True
    
    def test_access_token(self):
        """اختبار الحصول على Access Token"""
        print("\n🔑 اختبار Access Token...")
        print("=" * 50)
        
        try:
            # طريقة 1: App Access Token
            url = f"{self.base_url}/oauth/access_token"
            params = {
                'client_id': self.app_id,
                'client_secret': self.app_secret,
                'grant_type': 'client_credentials'
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get('access_token')
                print(f"✅ تم الحصول على Access Token بنجاح")
                print(f"📄 Token: {self.access_token[:50]}...")
                return True
            else:
                print(f"❌ فشل في الحصول على Access Token")
                print(f"📄 الاستجابة: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return False
    
    def test_app_permissions(self):
        """اختبار صلاحيات التطبيق"""
        print("\n🛡️ اختبار صلاحيات التطبيق...")
        print("=" * 50)
        
        if not self.access_token:
            print("❌ لا يوجد Access Token للاختبار")
            return False
        
        try:
            # اختبار الوصول لمعلومات التطبيق
            url = f"{self.base_url}/{self.app_id}"
            params = {
                'fields': 'id,name,category,restrictions',
                'access_token': self.access_token
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                app_data = response.json()
                print(f"✅ اسم التطبيق: {app_data.get('name', 'غير محدد')}")
                print(f"✅ فئة التطبيق: {app_data.get('category', 'غير محدد')}")
                
                restrictions = app_data.get('restrictions', {})
                if restrictions:
                    print(f"⚠️ قيود على التطبيق: {restrictions}")
                else:
                    print(f"✅ لا توجد قيود على التطبيق")
                
                return True
            else:
                print(f"❌ فشل في الوصول لمعلومات التطبيق")
                print(f"📄 الاستجابة: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الصلاحيات: {e}")
            return False
    
    def test_page_access(self, page_id="facebook"):
        """اختبار الوصول لصفحة معينة"""
        print(f"\n📄 اختبار الوصول للصفحة: {page_id}")
        print("=" * 50)
        
        if not self.access_token:
            print("❌ لا يوجد Access Token للاختبار")
            return False
        
        try:
            # اختبار الوصول الأساسي للصفحة
            url = f"{self.base_url}/{page_id}"
            params = {
                'fields': 'id,name,category,fan_count',
                'access_token': self.access_token
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                page_data = response.json()
                print(f"✅ اسم الصفحة: {page_data.get('name', 'غير محدد')}")
                print(f"✅ فئة الصفحة: {page_data.get('category', 'غير محدد')}")
                print(f"✅ عدد المتابعين: {page_data.get('fan_count', 'غير محدد')}")
                
                # اختبار الوصول للمنشورات
                return self.test_page_posts(page_id)
            else:
                error_data = response.json() if response.content else {}
                error_message = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                error_code = error_data.get('error', {}).get('code', 'غير محدد')
                
                print(f"❌ فشل في الوصول للصفحة")
                print(f"📄 رمز الخطأ: {error_code}")
                print(f"📝 الرسالة: {error_message}")
                
                # تشخيص المشكلة
                self.diagnose_error(error_code, error_message)
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الصفحة: {e}")
            return False
    
    def test_page_posts(self, page_id):
        """اختبار الوصول لمنشورات الصفحة"""
        print(f"\n📝 اختبار الوصول لمنشورات الصفحة...")
        
        try:
            url = f"{self.base_url}/{page_id}/posts"
            params = {
                'fields': 'id,message,created_time',
                'limit': 5,
                'access_token': self.access_token
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                posts_data = response.json()
                posts = posts_data.get('data', [])
                print(f"✅ تم جلب {len(posts)} منشور بنجاح")
                
                if posts:
                    print(f"📄 أحدث منشور: {posts[0].get('created_time', 'غير محدد')}")
                
                return True
            else:
                error_data = response.json() if response.content else {}
                error_message = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                error_code = error_data.get('error', {}).get('code', 'غير محدد')
                
                print(f"❌ فشل في الوصول للمنشورات")
                print(f"📄 رمز الخطأ: {error_code}")
                print(f"📝 الرسالة: {error_message}")
                
                self.diagnose_error(error_code, error_message)
                return False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار المنشورات: {e}")
            return False
    
    def diagnose_error(self, error_code, error_message):
        """تشخيص الأخطاء وتقديم حلول"""
        print(f"\n🔍 تشخيص المشكلة...")
        print("=" * 30)
        
        if error_code == 100:
            print("🔴 المشكلة: نقص في الصلاحيات")
            print("💡 السبب: التطبيق يحتاج صلاحيات إضافية من Facebook")
            print("🔧 الحلول:")
            print("   1. اذهب إلى Facebook Developers Console")
            print("   2. اطلب 'Page Public Content Access' feature")
            print("   3. اطلب 'pages_read_engagement' permission")
            print("   4. قدم طلب مراجعة للتطبيق")
            
        elif error_code == 190:
            print("🔴 المشكلة: Access Token غير صالح")
            print("💡 السبب: الـ Token منتهي الصلاحية أو غير صحيح")
            print("🔧 الحلول:")
            print("   1. احصل على Access Token جديد")
            print("   2. تحقق من صحة App ID و App Secret")
            print("   3. استخدم Graph API Explorer للحصول على token جديد")
            
        elif error_code == 803:
            print("🔴 المشكلة: الصفحة غير موجودة أو محمية")
            print("💡 السبب: الصفحة قد تكون خاصة أو محذوفة")
            print("🔧 الحلول:")
            print("   1. تأكد من صحة ID الصفحة")
            print("   2. جرب صفحات عامة ومشهورة")
            print("   3. تأكد أن الصفحة متاحة للجمهور")
            
        else:
            print(f"🔴 خطأ غير معروف: {error_code}")
            print("🔧 الحلول العامة:")
            print("   1. راجع Facebook API Documentation")
            print("   2. تحقق من حالة Facebook API")
            print("   3. جرب مرة أخرى بعد قليل")
    
    def run_full_test(self):
        """تشغيل اختبار شامل"""
        print("🧪 بدء الاختبار الشامل لـ Facebook API")
        print("=" * 60)
        
        # اختبار بيانات التطبيق
        if not self.test_app_credentials():
            return False
        
        # اختبار Access Token
        if not self.test_access_token():
            return False
        
        # اختبار صلاحيات التطبيق
        self.test_app_permissions()
        
        # اختبار الوصول لصفحات مختلفة
        test_pages = ["facebook", "cnn", "bbc"]
        
        for page in test_pages:
            success = self.test_page_access(page)
            if success:
                print(f"✅ الصفحة {page} تعمل بشكل صحيح")
                break
        else:
            print("❌ فشل في الوصول لجميع الصفحات المختبرة")
        
        print(f"\n📋 ملخص النتائج:")
        print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📄 راجع ملف FACEBOOK_API_SETUP.md للحلول التفصيلية")

def main():
    """الدالة الرئيسية"""
    tester = FacebookAPITester()
    tester.run_full_test()

if __name__ == "__main__":
    main()
