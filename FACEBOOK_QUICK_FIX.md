# 🚀 الحل السريع لمشكلة Facebook API

## 🎯 الهدف
الحصول على User Access Token للتغلب على قيود الصلاحيات

## ⏰ الوقت المطلوب: 5 دقائق

---

## 📋 الخطوات التفصيلية

### **الخطوة 1: الذهاب إلى Graph API Explorer**
```
🔗 الرابط: https://developers.facebook.com/tools/explorer/
```

### **الخطوة 2: اختيار التطبيق**
1. في القائمة العلوية، اختر **"Facebook App"**
2. ابحث عن تطبيق **"inews"** (App ID: 1905779223574690)
3. اختره من القائمة

### **الخطوة 3: طلب الصلاحيات**
1. اضغط على **"Add a Permission"**
2. أض<PERSON> الصلاحيات التالية:
   ```
   ✅ public_profile
   ✅ pages_show_list
   ✅ pages_read_engagement (إذا كان متاح)
   ```

### **الخطوة 4: إنشاء Access Token**
1. اضغط **"Generate Access Token"**
2. سيطلب منك تسجيل الدخول لـ Facebook
3. وافق على الصلاحيات المطلوبة
4. **انسخ الـ Access Token** (يبدأ بـ EAAB...)

### **الخطوة 5: إضافة الـ Token للتطبيق**
1. افتح ملف `.env` في مجلد iNews
2. أضف السطر التالي:
   ```env
   FACEBOOK_USER_ACCESS_TOKEN=EAAB...الـ_Token_الذي_نسخته
   ```

### **الخطوة 6: تحديث الكود**
سأقوم بتحديث الكود ليستخدم الـ User Token تلقائياً.

---

## 🔧 تحديث الكود

### **تحديث inews_collector.py:**
```python
def get_access_token(self):
    """الحصول على Access Token مع دعم User Token"""
    
    # جرب User Access Token أولاً (الأولوية الأولى)
    user_token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    if user_token:
        print("🔑 استخدام User Access Token")
        return user_token
    
    # الطريقة التقليدية (App Access Token)
    try:
        url = f"https://graph.facebook.com/oauth/access_token"
        params = {
            'client_id': self.app_id,
            'client_secret': self.app_secret,
            'grant_type': 'client_credentials'
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            token_data = response.json()
            return token_data.get('access_token')
    except Exception as e:
        print(f"❌ خطأ في الحصول على App Token: {e}")
    
    return None
```

---

## 🧪 الاختبار

### **بعد إضافة الـ User Token:**
1. **أعد تشغيل التطبيق**
2. **اذهب إلى لوحة التحكم**
3. **جرب جمع الأخبار**
4. **يجب أن يعمل الآن!**

---

## ⚠️ ملاحظات مهمة

### **مدة صلاحية User Token:**
- ⏰ **ينتهي خلال ساعات** (عادة 1-2 ساعة)
- 🔄 **يحتاج تجديد دوري**
- 📱 **مرتبط بحسابك الشخصي**

### **للاستخدام طويل المدى:**
- 📄 **أنشئ صفحة فيسبوك خاصة بك**
- 🔑 **احصل على Page Access Token**
- ⏰ **صالح لفترة أطول**

---

## 🎯 الخطوة التالية

**بعد نجاح الحل السريع، يمكنك:**
1. **استخدام التطبيق فوراً**
2. **التخطيط للحل طويل المدى**
3. **طلب مراجعة من Facebook**

---

## 📞 إذا واجهت مشاكل

### **إذا لم يعمل User Token:**
1. **تأكد من نسخ الـ Token كاملاً**
2. **تأكد من عدم وجود مسافات إضافية**
3. **جرب إنشاء Token جديد**

### **إذا انتهت صلاحية الـ Token:**
1. **كرر الخطوات 1-4**
2. **احصل على Token جديد**
3. **حدث ملف .env**

---

## 🎉 النتيجة المتوقعة

### **بدلاً من:**
```
❌ Error Code: 100 - Missing permissions
```

### **ستحصل على:**
```
✅ تم جلب الأخبار بنجاح
✅ تم حفظ X خبر من Y مصدر
```

**🚀 ابدأ الآن واحصل على User Access Token!**
