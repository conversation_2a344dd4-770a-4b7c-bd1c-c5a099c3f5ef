{% extends "base.html" %}

{% block title %}الإعداد - جامع الأخبار من تلغرام{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="bi bi-gear text-primary"></i>
                    إعداد التطبيق
                </h2>
                <p class="card-text text-muted">
                    إعداد الاتصال بتلغرام وتكوين التطبيق
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- خطوات الإعداد -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ol"></i>
                    خطوات الإعداد
                </h5>
            </div>
            <div class="card-body">
                <!-- الخطوة 1 -->
                <div class="setup-step">
                    <h6 class="fw-bold">
                        <span class="badge bg-primary me-2">1</span>
                        الحصول على بيانات تلغرام API
                    </h6>
                    <p class="mb-3">احصل على API ID و API Hash من موقع تلغرام الرسمي:</p>
                    
                    <ol class="mb-3">
                        <li>اذهب إلى <a href="https://my.telegram.org/apps" target="_blank" class="text-primary">my.telegram.org/apps</a></li>
                        <li>سجل الدخول برقم هاتفك المرتبط بتلغرام</li>
                        <li>أنشئ تطبيق جديد بالمعلومات التالية:
                            <ul class="mt-2">
                                <li><strong>App title:</strong> Telegram News Collector</li>
                                <li><strong>Short name:</strong> news_collector</li>
                                <li><strong>Platform:</strong> Desktop</li>
                                <li><strong>Description:</strong> News collection from Telegram channels</li>
                            </ul>
                        </li>
                        <li>احفظ <code>api_id</code> و <code>api_hash</code></li>
                    </ol>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>مهم:</strong> هذه البيانات سرية ولا تشاركها مع أحد
                    </div>
                </div>
                
                <hr>
                
                <!-- الخطوة 2 -->
                <div class="setup-step">
                    <h6 class="fw-bold">
                        <span class="badge bg-success me-2">2</span>
                        تحديث ملف .env
                    </h6>
                    <p class="mb-3">أدخل البيانات في ملف .env:</p>
                    
                    <div class="bg-light p-3 rounded mb-3">
                        <code>
                            TELEGRAM_API_ID=YOUR_API_ID_HERE<br>
                            TELEGRAM_API_HASH=YOUR_API_HASH_HERE<br>
                            TELEGRAM_PHONE_NUMBER=YOUR_PHONE_NUMBER_HERE
                        </code>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>تنبيه:</strong> استخدم رقم الهاتف بالصيغة الدولية (مثل: +964xxxxxxxxx)
                    </div>
                </div>
                
                <hr>
                
                <!-- الخطوة 3 -->
                <div class="setup-step">
                    <h6 class="fw-bold">
                        <span class="badge bg-info me-2">3</span>
                        اختبار الاتصال
                    </h6>
                    <p class="mb-3">تأكد من صحة الإعدادات:</p>
                    
                    {% if session.logged_in %}
                    <button onclick="testConnection()" class="btn btn-info">
                        <i class="bi bi-wifi"></i>
                        اختبار الاتصال بتلغرام
                    </button>
                    {% else %}
                    <div class="alert alert-secondary">
                        <i class="bi bi-lock"></i>
                        يجب تسجيل الدخول أولاً لاختبار الاتصال
                        <a href="{{ url_for('login') }}" class="btn btn-sm btn-primary ms-2">تسجيل الدخول</a>
                    </div>
                    {% endif %}
                </div>
                
                <hr>
                
                <!-- الخطوة 4 -->
                <div class="setup-step">
                    <h6 class="fw-bold">
                        <span class="badge bg-warning me-2">4</span>
                        بدء جمع الأخبار
                    </h6>
                    <p class="mb-3">بعد نجاح الاختبار، يمكنك بدء جمع الأخبار:</p>
                    
                    {% if session.logged_in %}
                    <a href="{{ url_for('dashboard') }}" class="btn btn-success">
                        <i class="bi bi-speedometer2"></i>
                        اذهب للوحة التحكم
                    </a>
                    {% else %}
                    <div class="alert alert-secondary">
                        <i class="bi bi-lock"></i>
                        يجب تسجيل الدخول أولاً للوصول للوحة التحكم
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="col-lg-4">
        <!-- متطلبات النظام -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-cpu"></i>
                    متطلبات النظام
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        Python 3.8+
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        حساب تلغرام نشط
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success"></i>
                        اتصال إنترنت مستقر
                    </li>
                    <li>
                        <i class="bi bi-check-circle text-success"></i>
                        مساحة تخزين كافية
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- نصائح الأمان -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-shield-check"></i>
                    نصائح الأمان
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-exclamation-triangle text-warning"></i>
                        لا تشارك بيانات API مع أحد
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-lock text-primary"></i>
                        احفظ نسخة احتياطية من ملف .env
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-eye-slash text-info"></i>
                        لا تنشر ملف .env على GitHub
                    </li>
                    <li>
                        <i class="bi bi-key text-success"></i>
                        غير كلمة مرور الإدارة
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- الدعم -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-question-circle"></i>
                    تحتاج مساعدة؟
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-3">إذا واجهت أي مشاكل في الإعداد:</p>
                
                <div class="d-grid gap-2">
                    <a href="https://core.telegram.org/api/obtaining_api_id" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-book"></i>
                        دليل تلغرام الرسمي
                    </a>
                    
                    <button onclick="showLogs()" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-file-text"></i>
                        عرض سجل الأخطاء
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض السجلات -->
<div class="modal fade" id="logsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-text"></i>
                    سجل الأخطاء
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="logsContent" class="bg-dark text-light p-3 rounded" style="height: 400px; overflow-y: auto; font-family: monospace;">
                    <div class="text-center text-muted">
                        <i class="bi bi-hourglass-split"></i>
                        جاري تحميل السجلات...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function testConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري الاختبار...';
    btn.disabled = true;
    
    fetch('/api/test_connection', {method: 'POST'})
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showAlert('danger', 'خطأ في الاتصال: ' + data.error);
            } else {
                showAlert('success', 'تم الاتصال بتلغرام بنجاح! يمكنك الآن بدء جمع الأخبار.');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('danger', 'حدث خطأ في اختبار الاتصال');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.row'));
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showLogs() {
    const modal = new bootstrap.Modal(document.getElementById('logsModal'));
    const logsContent = document.getElementById('logsContent');
    
    // محاكاة تحميل السجلات (يمكن تطويرها لاحقاً)
    setTimeout(() => {
        logsContent.innerHTML = `
            <div class="text-muted">2024-12-19 10:30:15 - INFO - بدء تشغيل التطبيق</div>
            <div class="text-muted">2024-12-19 10:30:16 - INFO - تحميل إعدادات البيئة</div>
            <div class="text-warning">2024-12-19 10:30:17 - WARNING - لم يتم العثور على بيانات تلغرام API</div>
            <div class="text-muted">2024-12-19 10:30:18 - INFO - تشغيل الخادم على المنفذ 5020</div>
            <div class="text-info">2024-12-19 10:35:22 - INFO - محاولة اتصال جديدة بتلغرام</div>
            <div class="text-muted">--- نهاية السجل ---</div>
        `;
    }, 1000);
    
    modal.show();
}
</script>
{% endblock %}
