"""File generated by TLObjects' generator. All changes will be ERASED"""
from ...tl.tlobject import TLObject
from typing import Optional, List, Union, TYPE_CHECKING
import os
import struct
from datetime import datetime
if TYPE_CHECKING:
    from ...tl.types import TypeStoryI<PERSON>, TypeStoryView, TypeStoryViews, TypeUser, TypeUserStories



class AllStories(TLObject):
    CONSTRUCTOR_ID = 0x839e0428
    SUBCLASS_OF_ID = 0x7e60d0cd

    def __init__(self, count: int, state: str, user_stories: List['TypeUserStories'], users: List['TypeUser'], has_more: Optional[bool]=None):
        """
        Constructor for stories.AllStories: Instance of either AllStoriesNotModified, AllStories.
        """
        self.count = count
        self.state = state
        self.user_stories = user_stories
        self.users = users
        self.has_more = has_more

    def to_dict(self):
        return {
            '_': 'AllStories',
            'count': self.count,
            'state': self.state,
            'user_stories': [] if self.user_stories is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.user_stories],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'has_more': self.has_more
        }

    def _bytes(self):
        return b''.join((
            b'(\x04\x9e\x83',
            struct.pack('<I', (0 if self.has_more is None or self.has_more is False else 1)),
            struct.pack('<i', self.count),
            self.serialize_bytes(self.state),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.user_stories)),b''.join(x._bytes() for x in self.user_stories),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _has_more = bool(flags & 1)
        _count = reader.read_int()
        _state = reader.tgread_string()
        reader.read_int()
        _user_stories = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _user_stories.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(count=_count, state=_state, user_stories=_user_stories, users=_users, has_more=_has_more)


class AllStoriesNotModified(TLObject):
    CONSTRUCTOR_ID = 0x47e0a07e
    SUBCLASS_OF_ID = 0x7e60d0cd

    def __init__(self, state: str):
        """
        Constructor for stories.AllStories: Instance of either AllStoriesNotModified, AllStories.
        """
        self.state = state

    def to_dict(self):
        return {
            '_': 'AllStoriesNotModified',
            'state': self.state
        }

    def _bytes(self):
        return b''.join((
            b'~\xa0\xe0G',
            self.serialize_bytes(self.state),
        ))

    @classmethod
    def from_reader(cls, reader):
        _state = reader.tgread_string()
        return cls(state=_state)


class Stories(TLObject):
    CONSTRUCTOR_ID = 0x4fe57df1
    SUBCLASS_OF_ID = 0x251c0c2c

    def __init__(self, count: int, stories: List['TypeStoryItem'], users: List['TypeUser']):
        """
        Constructor for stories.Stories: Instance of Stories.
        """
        self.count = count
        self.stories = stories
        self.users = users

    def to_dict(self):
        return {
            '_': 'Stories',
            'count': self.count,
            'stories': [] if self.stories is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.stories],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'\xf1}\xe5O',
            struct.pack('<i', self.count),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.stories)),b''.join(x._bytes() for x in self.stories),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        _count = reader.read_int()
        reader.read_int()
        _stories = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _stories.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(count=_count, stories=_stories, users=_users)


class StoryViews(TLObject):
    CONSTRUCTOR_ID = 0xde9eed1d
    SUBCLASS_OF_ID = 0x4b3fc4ba

    def __init__(self, views: List['TypeStoryViews'], users: List['TypeUser']):
        """
        Constructor for stories.StoryViews: Instance of StoryViews.
        """
        self.views = views
        self.users = users

    def to_dict(self):
        return {
            '_': 'StoryViews',
            'views': [] if self.views is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.views],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'\x1d\xed\x9e\xde',
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.views)),b''.join(x._bytes() for x in self.views),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        reader.read_int()
        _views = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _views.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(views=_views, users=_users)


class StoryViewsList(TLObject):
    CONSTRUCTOR_ID = 0xfb3f77ac
    SUBCLASS_OF_ID = 0xb9437560

    def __init__(self, count: int, views: List['TypeStoryView'], users: List['TypeUser']):
        """
        Constructor for stories.StoryViewsList: Instance of StoryViewsList.
        """
        self.count = count
        self.views = views
        self.users = users

    def to_dict(self):
        return {
            '_': 'StoryViewsList',
            'count': self.count,
            'views': [] if self.views is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.views],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'\xacw?\xfb',
            struct.pack('<i', self.count),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.views)),b''.join(x._bytes() for x in self.views),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        _count = reader.read_int()
        reader.read_int()
        _views = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _views.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(count=_count, views=_views, users=_users)


class UserStories(TLObject):
    CONSTRUCTOR_ID = 0x37a6ff5f
    SUBCLASS_OF_ID = 0xc6b1923d

    def __init__(self, stories: 'TypeUserStories', users: List['TypeUser']):
        """
        Constructor for stories.UserStories: Instance of UserStories.
        """
        self.stories = stories
        self.users = users

    def to_dict(self):
        return {
            '_': 'UserStories',
            'stories': self.stories.to_dict() if isinstance(self.stories, TLObject) else self.stories,
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'_\xff\xa67',
            self.stories._bytes(),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        _stories = reader.tgread_object()
        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(stories=_stories, users=_users)

