#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوصول للصفحات العامة والأخبار
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

def test_public_pages_access():
    """اختبار الوصول للصفحات العامة"""
    print("🔍 اختبار الوصول للصفحات العامة")
    print("=" * 60)
    
    token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    
    if not token:
        print("❌ لا يوجد User Access Token")
        return False
    
    # صفحات أخبار عامة مشهورة
    public_pages = [
        {'id': 'BBCArabic', 'name': 'BBC Arabic'},
        {'id': 'AlJazeeraChannel', 'name': 'Al Jazeera Channel'},
        {'id': 'SkyNewsArabia', 'name': 'Sky News Arabia'},
        {'id': 'CNNArabic', 'name': 'CNN Arabic'},
        {'id': 'AlArabiya', 'name': 'Al Arabiya'},
        {'id': 'facebook', 'name': 'Facebook (للاختبار)'},
        {'id': 'meta', 'name': 'Meta (للاختبار)'}
    ]
    
    successful_pages = []
    
    for page in public_pages:
        print(f"\n📄 اختبار صفحة: {page['name']} ({page['id']})")
        
        try:
            # اختبار الوصول لمعلومات الصفحة
            page_url = f"https://graph.facebook.com/v18.0/{page['id']}"
            page_params = {
                'access_token': token,
                'fields': 'id,name,category,fan_count,about,website'
            }
            
            page_response = requests.get(page_url, params=page_params, timeout=10)
            
            if page_response.status_code == 200:
                page_data = page_response.json()
                print(f"   ✅ معلومات الصفحة:")
                print(f"      📝 الاسم: {page_data.get('name', 'غير محدد')}")
                print(f"      📂 الفئة: {page_data.get('category', 'غير محدد')}")
                print(f"      👥 المتابعون: {page_data.get('fan_count', 'غير محدد')}")
                
                # اختبار الوصول للمنشورات
                posts_url = f"https://graph.facebook.com/v18.0/{page['id']}/posts"
                posts_params = {
                    'access_token': token,
                    'fields': 'id,message,created_time,link,type',
                    'limit': 3
                }
                
                posts_response = requests.get(posts_url, params=posts_params, timeout=10)
                
                if posts_response.status_code == 200:
                    posts_data = posts_response.json()
                    posts = posts_data.get('data', [])
                    print(f"      📰 المنشورات: {len(posts)} منشور متاح")
                    
                    if posts:
                        for i, post in enumerate(posts, 1):
                            message = post.get('message', post.get('story', 'بدون نص'))
                            if message:
                                preview = message[:50] + "..." if len(message) > 50 else message
                                print(f"         {i}. {preview}")
                    
                    successful_pages.append({
                        'id': page['id'],
                        'name': page_data.get('name', page['name']),
                        'posts_count': len(posts),
                        'can_access_posts': True
                    })
                    
                else:
                    posts_error = posts_response.json() if posts_response.content else {}
                    posts_msg = posts_error.get('error', {}).get('message', 'خطأ غير معروف')
                    print(f"      ❌ لا يمكن الوصول للمنشورات: {posts_msg}")
                    
                    successful_pages.append({
                        'id': page['id'],
                        'name': page_data.get('name', page['name']),
                        'posts_count': 0,
                        'can_access_posts': False
                    })
                    
            else:
                page_error = page_response.json() if page_response.content else {}
                page_msg = page_error.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"   ❌ لا يمكن الوصول للصفحة: {page_msg}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
    
    # ملخص النتائج
    print(f"\n📊 ملخص النتائج:")
    print("=" * 60)
    
    accessible_pages = [p for p in successful_pages if p['can_access_posts']]
    
    if accessible_pages:
        print(f"✅ صفحات يمكن الوصول لمنشوراتها ({len(accessible_pages)}):")
        for page in accessible_pages:
            print(f"   📄 {page['name']} - {page['posts_count']} منشور")
    
    info_only_pages = [p for p in successful_pages if not p['can_access_posts']]
    if info_only_pages:
        print(f"\n⚠️ صفحات يمكن الوصول لمعلوماتها فقط ({len(info_only_pages)}):")
        for page in info_only_pages:
            print(f"   📄 {page['name']} - معلومات فقط")
    
    return successful_pages

def test_alternative_methods():
    """اختبار طرق بديلة للوصول للأخبار"""
    print(f"\n🔄 اختبار طرق بديلة...")
    print("=" * 60)
    
    token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    
    # طرق بديلة
    alternative_tests = [
        {
            'name': 'البحث عن منشورات عامة',
            'url': 'https://graph.facebook.com/v18.0/search',
            'params': {
                'q': 'news',
                'type': 'post',
                'limit': 5
            }
        },
        {
            'name': 'البحث عن صفحات الأخبار',
            'url': 'https://graph.facebook.com/v18.0/search',
            'params': {
                'q': 'news',
                'type': 'page',
                'limit': 10
            }
        }
    ]
    
    for test in alternative_tests:
        print(f"\n🔍 {test['name']}:")
        
        try:
            params = {'access_token': token}
            params.update(test['params'])
            
            response = requests.get(test['url'], params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                items = data.get('data', [])
                print(f"   ✅ نجح! عدد النتائج: {len(items)}")
                
                for i, item in enumerate(items[:3], 1):
                    name = item.get('name', item.get('message', 'بدون اسم'))
                    if name:
                        preview = name[:50] + "..." if len(name) > 50 else name
                        print(f"      {i}. {preview}")
                        
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"   ❌ فشل: {error_msg}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")

def suggest_solutions(successful_pages):
    """اقتراح حلول بناء على النتائج"""
    print(f"\n💡 الحلول المقترحة:")
    print("=" * 60)
    
    accessible_pages = [p for p in successful_pages if p['can_access_posts']]
    
    if accessible_pages:
        print(f"🎯 **الحل الحالي (يعمل الآن):**")
        print(f"   • لديك وصول لـ {len(accessible_pages)} صفحة")
        print(f"   • يمكن جمع الأخبار من هذه الصفحات")
        print(f"   • استخدم هذه الصفحات في التطبيق")
        
        print(f"\n📋 **الصفحات المتاحة:**")
        for page in accessible_pages:
            print(f"   • {page['name']} (ID: {page['id']})")
        
        print(f"\n🚀 **الخطوة التالية:**")
        print(f"   1. سأحدث قائمة الصفحات في التطبيق")
        print(f"   2. جرب جمع الأخبار من هذه الصفحات")
        
    else:
        print(f"❌ **لا يوجد وصول للمنشورات حالياً**")
        print(f"")
        print(f"🔧 **الحلول البديلة:**")
        print(f"")
        print(f"1️⃣ **إنشاء صفحة Facebook (الأفضل):**")
        print(f"   • أنشئ صفحة Facebook باسمك")
        print(f"   • احصل على Page Access Token")
        print(f"   • Page Token له صلاحيات أكثر")
        print(f"")
        print(f"2️⃣ **طلب صلاحيات إضافية:**")
        print(f"   • ارجع لـ Graph API Explorer")
        print(f"   • جرب إضافة صلاحيات أخرى")
        print(f"   • بعض الصلاحيات قد تحتاج موافقة Facebook")
        print(f"")
        print(f"3️⃣ **استخدام RSS feeds (بديل):**")
        print(f"   • مصادر أخبار عبر RSS")
        print(f"   • لا يحتاج صلاحيات Facebook")
        print(f"   • يمكن تنفيذه بسهولة")

def update_app_with_working_pages(successful_pages):
    """تحديث التطبيق بالصفحات التي تعمل"""
    accessible_pages = [p for p in successful_pages if p['can_access_posts']]
    
    if not accessible_pages:
        print(f"\n❌ لا توجد صفحات متاحة للتحديث")
        return False
    
    print(f"\n🔄 تحديث قائمة الصفحات في التطبيق...")
    
    # إنشاء قائمة الصفحات المحدثة
    updated_pages = []
    for page in accessible_pages:
        updated_pages.append({
            'id': page['id'],
            'name': page['name'],
            'active': True,
            'category': 'news',
            'verified': True
        })
    
    # حفظ القائمة المحدثة
    try:
        with open('working_pages.json', 'w', encoding='utf-8') as f:
            json.dump(updated_pages, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ {len(updated_pages)} صفحة في working_pages.json")
        print(f"📋 الصفحات المحفوظة:")
        for page in updated_pages:
            print(f"   • {page['name']} (ID: {page['id']})")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في حفظ القائمة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار الوصول لأخبار الصفحات العامة")
    print("=" * 60)
    print("🎯 الهدف: العثور على صفحات يمكن الوصول لمنشوراتها")
    print("=" * 60)
    
    # اختبار الصفحات العامة
    successful_pages = test_public_pages_access()
    
    # اختبار طرق بديلة
    test_alternative_methods()
    
    # اقتراح حلول
    suggest_solutions(successful_pages)
    
    # تحديث التطبيق إذا وجدت صفحات تعمل
    if successful_pages:
        accessible_pages = [p for p in successful_pages if p['can_access_posts']]
        if accessible_pages:
            update_app_with_working_pages(successful_pages)
            
            print(f"\n🎉 تم! يمكنك الآن:")
            print(f"   1. شغل التطبيق: python app.py")
            print(f"   2. جرب جمع الأخبار من الصفحات المتاحة")
            print(f"   3. استخدم working_pages.json كمرجع")
    
    print(f"\n📋 الخطوات التالية:")
    print(f"1. راجع النتائج أعلاه")
    print(f"2. جرب التطبيق مع الصفحات المتاحة")
    print(f"3. إذا لم تعمل، فكر في إنشاء صفحة Facebook")

if __name__ == "__main__":
    main()
