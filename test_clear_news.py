#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دالة تنظيف الأخبار
"""

import requests
import json

def test_clear_news():
    """اختبار دالة تنظيف الأخبار"""
    print("🧪 اختبار دالة تنظيف الأخبار...")
    print("=" * 50)
    
    # رابط التطبيق
    base_url = "http://localhost:5020"
    
    # اختبار الاتصال أولاً
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ التطبيق يعمل ويمكن الوصول إليه")
        else:
            print(f"⚠️ التطبيق يعمل لكن هناك مشكلة: {response.status_code}")
    except Exception as e:
        print(f"❌ لا يمكن الوصول للتطبيق: {e}")
        print("💡 تأكد من تشغيل التطبيق على http://localhost:5020")
        return False
    
    # اختبار دالة تنظيف الأخبار
    print("\n🗑️ اختبار دالة تنظيف الأخبار...")
    
    try:
        # إرسال طلب تنظيف
        clear_url = f"{base_url}/clear_news"
        headers = {
            'Content-Type': 'application/json'
        }
        data = {}
        
        response = requests.post(clear_url, 
                               headers=headers, 
                               data=json.dumps(data),
                               timeout=10)
        
        print(f"📄 رمز الاستجابة: {response.status_code}")
        print(f"📝 محتوى الاستجابة: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('success'):
                    print(f"✅ نجح التنظيف: {result.get('message')}")
                    return True
                else:
                    print(f"❌ فشل التنظيف: {result.get('message')}")
                    return False
            except json.JSONDecodeError:
                print("❌ الاستجابة ليست JSON صحيح")
                return False
        elif response.status_code == 401:
            print("🔒 مطلوب تسجيل دخول - هذا طبيعي")
            print("💡 الدالة محمية بنظام الأمان")
            return True
        else:
            print(f"❌ خطأ في الخادم: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_with_session():
    """اختبار مع جلسة تسجيل دخول"""
    print("\n🔐 اختبار مع تسجيل الدخول...")
    
    base_url = "http://localhost:5020"
    session = requests.Session()
    
    try:
        # محاولة تسجيل الدخول
        login_data = {
            'password': 'YaserAdmin2024!SecureNews@Protection'
        }
        
        login_response = session.post(f"{base_url}/login", data=login_data)
        
        if login_response.status_code == 200:
            print("✅ تم تسجيل الدخول بنجاح")
            
            # الآن جرب تنظيف الأخبار
            clear_response = session.post(f"{base_url}/clear_news", 
                                        headers={'Content-Type': 'application/json'},
                                        data=json.dumps({}))
            
            print(f"📄 رمز استجابة التنظيف: {clear_response.status_code}")
            print(f"📝 محتوى الاستجابة: {clear_response.text}")
            
            if clear_response.status_code == 200:
                try:
                    result = clear_response.json()
                    if result.get('success'):
                        print(f"✅ نجح التنظيف مع تسجيل الدخول: {result.get('message')}")
                        return True
                    else:
                        print(f"❌ فشل التنظيف: {result.get('message')}")
                        return False
                except json.JSONDecodeError:
                    print("❌ الاستجابة ليست JSON صحيح")
                    return False
            else:
                print(f"❌ خطأ في التنظيف: {clear_response.status_code}")
                return False
        else:
            print(f"❌ فشل تسجيل الدخول: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الجلسة: {e}")
        return False

def check_news_files():
    """فحص ملفات الأخبار الموجودة"""
    print("\n📁 فحص ملفات الأخبار...")
    
    import os
    
    secure_data_dir = 'secure_data'
    
    if not os.path.exists(secure_data_dir):
        print("📂 مجلد secure_data غير موجود")
        return 0
    
    news_files = []
    for filename in os.listdir(secure_data_dir):
        if (filename.startswith('facebook_news_') or filename.startswith('rss_news_')) and filename.endswith('.json'):
            news_files.append(filename)
    
    print(f"📊 عدد ملفات الأخبار الموجودة: {len(news_files)}")
    
    if news_files:
        print("📄 ملفات الأخبار:")
        for file in news_files[:5]:  # عرض أول 5 ملفات
            print(f"   • {file}")
        if len(news_files) > 5:
            print(f"   ... و {len(news_files) - 5} ملف آخر")
    
    return len(news_files)

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لدالة تنظيف الأخبار")
    print("=" * 60)
    
    # فحص ملفات الأخبار قبل التنظيف
    files_before = check_news_files()
    
    # اختبار بدون تسجيل دخول
    test1_result = test_clear_news()
    
    # اختبار مع تسجيل دخول
    test2_result = test_with_session()
    
    # فحص ملفات الأخبار بعد التنظيف
    files_after = check_news_files()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج")
    print("=" * 60)
    print(f"📁 ملفات الأخبار قبل التنظيف: {files_before}")
    print(f"📁 ملفات الأخبار بعد التنظيف: {files_after}")
    print(f"🧪 اختبار بدون تسجيل دخول: {'✅ نجح' if test1_result else '❌ فشل'}")
    print(f"🔐 اختبار مع تسجيل دخول: {'✅ نجح' if test2_result else '❌ فشل'}")
    
    if test2_result and files_before > files_after:
        print("\n🎉 دالة تنظيف الأخبار تعمل بشكل صحيح!")
    elif test1_result and not test2_result:
        print("\n⚠️ الدالة محمية بنظام الأمان (هذا طبيعي)")
    else:
        print("\n❌ هناك مشكلة في دالة تنظيف الأخبار")
        print("💡 تحقق من:")
        print("   1. تشغيل التطبيق على http://localhost:5020")
        print("   2. صحة كلمة المرور")
        print("   3. وجود ملفات أخبار للحذف")

if __name__ == "__main__":
    main()
