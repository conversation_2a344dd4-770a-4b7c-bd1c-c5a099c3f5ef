@echo off
echo ========================================
echo 🚀 بدء تشغيل iNews - نظام جمع الأخبار الآمن
echo ========================================
echo.

cd /d "g:\inews"

echo 📋 إنشاء البيئة الافتراضية إذا لم تكن موجودة...
if not exist "venv" (
    echo 🔧 إنشاء بيئة افتراضية جديدة...
    python -m venv venv
)

echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

echo 📋 التحقق من المتطلبات...
python -c "import flask, cryptography, requests, pandas, feedparser; print('✅ جميع المكتبات متوفرة')" 2>nul
if errorlevel 1 (
    echo ❌ بعض المكتبات مفقودة. جاري التثبيت...
    pip install -r requirements.txt
    pip install feedparser
)

echo.
echo 🔧 بدء تشغيل الخادم...
echo 📱 الواجهة ستكون متاحة على: http://localhost:5020
echo 🔑 كلمة مرور المدير: YaserAdmin2024!SecureNews@Protection
echo.
echo ⚠️  لإيقاف الخادم اضغط Ctrl+C
echo ========================================
echo.

python app.py

pause
