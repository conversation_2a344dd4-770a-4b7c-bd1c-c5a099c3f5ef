#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح صلاحيات Facebook API للوصول للصفحات المتابعة
"""

import webbrowser
import requests
import os
from dotenv import load_dotenv

load_dotenv()

def check_current_permissions():
    """فحص الصلاحيات الحالية للـ User Token"""
    print("🔍 فحص الصلاحيات الحالية...")
    print("=" * 50)
    
    token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    if not token:
        print("❌ لا يوجد User Access Token في ملف .env")
        return False
    
    try:
        # فحص الصلاحيات
        url = "https://graph.facebook.com/v18.0/me/permissions"
        params = {'access_token': token}
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            permissions = data.get('data', [])
            
            print(f"✅ تم العثور على {len(permissions)} صلاحية:")
            
            granted_permissions = []
            for perm in permissions:
                status = perm.get('status', 'unknown')
                permission = perm.get('permission', 'unknown')
                
                if status == 'granted':
                    granted_permissions.append(permission)
                    print(f"   ✅ {permission}")
                else:
                    print(f"   ❌ {permission} ({status})")
            
            # فحص الصلاحيات المطلوبة
            required_permissions = [
                'user_posts',
                'user_likes', 
                'pages_show_list',
                'pages_read_engagement',
                'read_page_mailboxes'
            ]
            
            print(f"\n📋 الصلاحيات المطلوبة:")
            missing_permissions = []
            for req_perm in required_permissions:
                if req_perm in granted_permissions:
                    print(f"   ✅ {req_perm}")
                else:
                    print(f"   ❌ {req_perm} - مفقودة")
                    missing_permissions.append(req_perm)
            
            if missing_permissions:
                print(f"\n⚠️ تحتاج إلى {len(missing_permissions)} صلاحية إضافية")
                return False
            else:
                print(f"\n🎉 جميع الصلاحيات متوفرة!")
                return True
                
        else:
            print(f"❌ فشل في فحص الصلاحيات: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص الصلاحيات: {e}")
        return False

def open_permissions_url():
    """فتح رابط Graph API Explorer مع الصلاحيات المطلوبة"""
    print("\n🔗 فتح Graph API Explorer مع الصلاحيات المطلوبة...")
    
    app_id = "1905779223574690"
    
    # الصلاحيات المطلوبة
    permissions = [
        'public_profile',
        'user_posts',
        'user_likes',
        'pages_show_list',
        'pages_read_engagement',
        'read_page_mailboxes'
    ]
    
    # بناء URL مع الصلاحيات
    permissions_str = ','.join(permissions)
    url = f"https://developers.facebook.com/tools/explorer/{app_id}/?method=GET&path=me%2Ffeed&version=v18.0"
    
    try:
        webbrowser.open(url)
        print(f"✅ تم فتح الرابط في المتصفح")
        
        print(f"\n📋 الخطوات المطلوبة:")
        print(f"1. في Graph API Explorer، اضغط 'Add a Permission'")
        print(f"2. أضف الصلاحيات التالية:")
        for perm in permissions:
            print(f"   • {perm}")
        print(f"3. اضغط 'Generate Access Token'")
        print(f"4. سجل الدخول ووافق على جميع الصلاحيات")
        print(f"5. انسخ الـ Access Token الجديد")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في فتح المتصفح: {e}")
        print(f"🔗 افتح هذا الرابط يدوياً: {url}")
        return False

def update_token():
    """تحديث الـ User Token في ملف .env"""
    print(f"\n🔄 تحديث User Access Token...")
    print("=" * 50)
    
    new_token = input("🔑 الصق الـ Access Token الجديد هنا: ").strip()
    
    if not new_token:
        print("❌ لم يتم إدخال token")
        return False
    
    if not new_token.startswith('EAAB'):
        print("⚠️ تحذير: الـ Token لا يبدأ بـ EAAB، تأكد من صحته")
    
    try:
        # قراءة ملف .env
        env_file = ".env"
        lines = []
        
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        
        # تحديث أو إضافة FACEBOOK_USER_ACCESS_TOKEN
        token_line = f"FACEBOOK_USER_ACCESS_TOKEN={new_token}\n"
        found = False
        
        for i, line in enumerate(lines):
            if line.startswith('FACEBOOK_USER_ACCESS_TOKEN='):
                lines[i] = token_line
                found = True
                break
        
        if not found:
            lines.append(token_line)
        
        # حفظ الملف
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم تحديث الـ Token في ملف .env")
        
        # إعادة تحميل متغيرات البيئة
        load_dotenv(override=True)
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في تحديث الـ Token: {e}")
        return False

def test_new_token():
    """اختبار الـ Token الجديد"""
    print(f"\n🧪 اختبار الـ Token الجديد...")
    print("=" * 50)
    
    token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    
    if not token:
        print("❌ لا يوجد token للاختبار")
        return False
    
    # اختبارات مختلفة
    tests = [
        {
            'name': 'معلومات المستخدم',
            'url': 'https://graph.facebook.com/v18.0/me',
            'params': {'fields': 'id,name'}
        },
        {
            'name': 'الصفحات المعجب بها',
            'url': 'https://graph.facebook.com/v18.0/me/likes',
            'params': {'limit': 5}
        },
        {
            'name': 'خلاصة المستخدم',
            'url': 'https://graph.facebook.com/v18.0/me/feed',
            'params': {'limit': 3}
        },
        {
            'name': 'منشورات المستخدم',
            'url': 'https://graph.facebook.com/v18.0/me/posts',
            'params': {'limit': 3}
        }
    ]
    
    successful_tests = 0
    
    for test in tests:
        print(f"\n🔍 اختبار: {test['name']}")
        
        try:
            params = {'access_token': token}
            params.update(test['params'])
            
            response = requests.get(test['url'], params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data:
                    items = data['data']
                    print(f"   ✅ نجح! عدد العناصر: {len(items)}")
                    if items and 'name' in items[0]:
                        print(f"   📝 مثال: {items[0]['name']}")
                else:
                    print(f"   ✅ نجح! البيانات: {data.get('name', 'متوفر')}")
                
                successful_tests += 1
                
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"   ❌ فشل: {error_msg}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
    
    print(f"\n📊 النتائج: {successful_tests}/{len(tests)} اختبار نجح")
    
    if successful_tests >= 2:
        print(f"🎉 الـ Token يعمل بشكل جيد!")
        return True
    else:
        print(f"⚠️ الـ Token يحتاج تحسين")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح صلاحيات Facebook API")
    print("=" * 60)
    print("🎯 الهدف: الحصول على صلاحيات للوصول للصفحات المتابعة")
    print("=" * 60)
    
    # فحص الصلاحيات الحالية
    current_ok = check_current_permissions()
    
    if current_ok:
        print("\n🎉 الصلاحيات الحالية كافية!")
        print("🧪 جرب الآن جمع الأخبار من التطبيق")
        return
    
    print(f"\n💡 تحتاج إلى الحصول على صلاحيات إضافية")
    
    # فتح Graph API Explorer
    if open_permissions_url():
        input(f"\n⏸️ اضغط Enter بعد الحصول على الـ Access Token الجديد...")
        
        # تحديث الـ Token
        if update_token():
            # اختبار الـ Token الجديد
            if test_new_token():
                print(f"\n🎉 تم! يمكنك الآن استخدام التطبيق")
                print(f"🚀 جرب جمع الأخبار من لوحة التحكم")
            else:
                print(f"\n⚠️ الـ Token يعمل جزئياً، جرب التطبيق")
        else:
            print(f"\n❌ فشل في تحديث الـ Token")
    else:
        print(f"\n❌ فشل في فتح Graph API Explorer")
    
    print(f"\n📋 الخطوات التالية:")
    print(f"1. جرب التطبيق: http://localhost:5020")
    print(f"2. اذهب للوحة التحكم وجرب جمع الأخبار")
    print(f"3. إذا لم يعمل، كرر العملية مع صلاحيات أكثر")

if __name__ == "__main__":
    main()
