{% extends "base.html" %}

{% block title %}تسجيل الدخول - iNews{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                    <h3 class="fw-bold">تسجيل الدخول</h3>
                    <p class="text-muted">دخول آمن لإدارة النظام</p>
                </div>
                
                <form method="POST">
                    <div class="mb-4">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" 
                                   name="password" required placeholder="أدخل كلمة المرور">
                            <button class="btn btn-outline-secondary" type="button" 
                                    onclick="togglePassword()">
                                <i class="fas fa-eye" id="password-icon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        دخول
                    </button>
                </form>
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        نظام محمي بتشفير متقدم
                    </small>
                </div>
            </div>
        </div>
        
        <!-- معلومات الأمان -->
        <div class="card mt-4">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-shield-alt text-success me-2"></i>
                    ميزات الأمان
                </h6>
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تشفير البيانات بـ AES-256
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        حماية من الهجمات المتكررة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تسجيل جميع العمليات
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        جلسات آمنة ومحدودة الوقت
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('password-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'fas fa-eye';
    }
}

// التركيز على حقل كلمة المرور
$(document).ready(function() {
    $('#password').focus();
});

// منع إرسال النموذج إذا كانت كلمة المرور فارغة
$('form').on('submit', function(e) {
    const password = $('#password').val().trim();
    if (!password) {
        e.preventDefault();
        alert('يرجى إدخال كلمة المرور');
        $('#password').focus();
    }
});
</script>
{% endblock %}
