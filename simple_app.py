#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق ويب مبسط لجمع الأخبار من تلغرام
"""

try:
    from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
    print("✅ Flask imported successfully")
except ImportError as e:
    print(f"❌ Flask import error: {e}")
    exit(1)

try:
    import os
    from dotenv import load_dotenv
    print("✅ Basic modules imported successfully")
except ImportError as e:
    print(f"❌ Basic modules import error: {e}")
    exit(1)

# تحميل متغيرات البيئة
load_dotenv()

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'default_secret_key')

# متغيرات عامة
collection_status = {
    'is_running': False,
    'last_update': None,
    'total_news': 0,
    'channels_count': 0,
    'error_message': 'رقم الهاتف محظور من تلغرام - يرجى استخدام رقم آخر'
}

latest_news = []
channels_list = []

def check_admin_password(password):
    """التحقق من كلمة مرور الإدارة"""
    admin_password = os.getenv('ADMIN_PASSWORD', 'yaseraljebori@25m')
    return password == admin_password

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html', 
                         collection_status=collection_status,
                         latest_news=latest_news[:10])

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        password = request.form.get('password')
        if check_admin_password(password):
            session['logged_in'] = True
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.pop('logged_in', None)
    flash('تم تسجيل الخروج', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))
    
    return render_template('dashboard.html',
                         collection_status=collection_status,
                         channels_list=channels_list,
                         latest_news=latest_news[:20])

@app.route('/api/status')
def get_status():
    """الحصول على حالة جمع الأخبار"""
    return jsonify(collection_status)

@app.route('/news')
def news_page():
    """صفحة الأخبار"""
    return render_template('news.html', latest_news=latest_news)

@app.route('/setup')
def setup():
    """صفحة الإعداد الأولي"""
    return render_template('setup.html')

@app.route('/api/test_connection', methods=['POST'])
def test_connection():
    """اختبار الاتصال بتلغرام"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    # محاكاة اختبار الاتصال
    phone = os.getenv('TELEGRAM_PHONE_NUMBER', '')
    
    if not phone or phone == 'YOUR_PHONE_NUMBER_HERE':
        return jsonify({'error': 'رقم الهاتف غير محدد في ملف .env'}), 500
    
    if phone == '+9647714367586':
        return jsonify({'error': 'رقم الهاتف محظور من تلغرام - يرجى استخدام رقم آخر'}), 500
    
    return jsonify({'message': 'تم الاتصال بتلغرام بنجاح (محاكاة)'})

@app.route('/api/start_collection', methods=['POST'])
def start_collection():
    """بدء جمع الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    return jsonify({'error': 'رقم الهاتف محظور من تلغرام - يرجى استخدام رقم آخر'}), 500

@app.route('/api/stop_collection', methods=['POST'])
def stop_collection():
    """إيقاف جمع الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    collection_status['is_running'] = False
    return jsonify({'message': 'تم إيقاف جمع الأخبار'})

if __name__ == '__main__':
    print("🚀 بدء تشغيل تطبيق جمع الأخبار من تلغرام (نسخة مبسطة)")
    print("=" * 60)
    print("🌐 الواجهة متاحة على: http://localhost:5020")
    print("🔑 كلمة مرور الإدارة: yaseraljebori@25m")
    print("⚠️ ملاحظة: رقم الهاتف الحالي محظور من تلغرام")
    print("💡 يرجى استخدام رقم هاتف آخر مرتبط بحساب تلغرام نشط")
    print("=" * 60)
    
    # تشغيل التطبيق
    port = int(os.getenv('PORT', 5020))
    app.run(host='0.0.0.0', port=port, debug=True)
