/* ملف الأنماط المخصص لتطبيق جمع الأخبار من تلغرام */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* تحسينات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    line-height: 1.6;
}

/* شريط التنقل */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--box-shadow);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 20px;
    margin: 0 5px;
    padding: 8px 15px !important;
}

.navbar-nav .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color) !important;
}

/* البطاقات */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-bottom: none;
    font-weight: 600;
}

/* الأزرار */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #e83e8c);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #6f42c1);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

/* عناصر الأخبار */
.news-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.news-item:hover {
    transform: translateX(-5px);
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    border-left-color: var(--secondary-color);
}

.news-item p {
    margin-bottom: 15px;
    color: #333;
    line-height: 1.7;
}

/* شارات الحالة */
.status-badge {
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 0.9em;
    font-weight: 600;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.bg-success {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
    animation: pulse 2s infinite;
}

.status-badge.bg-danger {
    background: linear-gradient(45deg, #dc3545, #e83e8c) !important;
}

.status-badge.bg-secondary {
    background: linear-gradient(45deg, #6c757d, #495057) !important;
}

/* شارات القنوات */
.channel-tag {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 600;
    margin-right: 8px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
}

.input-group-text {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 10px 0 0 10px;
}

/* الجداول */
.table {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(45deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(45deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(45deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.1));
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(45deg, rgba(23, 162, 184, 0.1), rgba(111, 66, 193, 0.1));
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* التصفح (Pagination) */
.pagination .page-link {
    border-radius: 10px;
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
    background: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
}

.pagination .page-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
}

/* الرسوم المتحركة */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* خطوات الإعداد */
.setup-step {
    padding: 20px 0;
}

.setup-step h6 {
    color: #333;
    margin-bottom: 15px;
}

.setup-step .badge {
    font-size: 1rem;
    padding: 8px 12px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }
    
    .news-item {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 8px 20px;
        font-size: 0.9rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
}

/* تحسينات للطباعة */
@media print {
    body {
        background: white !important;
    }
    
    .navbar,
    .btn,
    .alert {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}
