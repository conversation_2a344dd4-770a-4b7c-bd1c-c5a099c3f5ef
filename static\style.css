/* iNews - تصميم مخصص */

body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

.container {
    max-width: 1200px;
}

.page-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    font-weight: bold;
}

.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.news-item {
    border-left: 4px solid #007bff;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.news-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stats-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.login-container {
    max-width: 400px;
    margin: 10vh auto;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.table {
    border-radius: 10px;
    overflow: hidden;
}

.badge {
    border-radius: 20px;
    padding: 0.5rem 1rem;
}

.progress {
    border-radius: 10px;
    height: 10px;
}

.alert {
    border-radius: 10px;
    border: none;
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 768px) {
    .page-header {
        padding: 1rem;
        text-align: center;
    }
    
    .container {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* تحسين الأيقونات */
.fas, .fab {
    margin-left: 0.5rem;
}

/* تحسين الجداول */
.table-responsive {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    border: none;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

/* تحسين شريط التقدم */
.progress-bar {
    border-radius: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
}

/* تحسين التنبيهات */
.alert-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border: none;
}

.alert-danger {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
    border: none;
}

.alert-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: white;
    border: none;
}

.alert-info {
    background: linear-gradient(45deg, #17a2b8, #6f42c1);
    color: white;
    border: none;
}
