"""
🧪 اختبار مصدر فيسبوك جديد
أداة لاختبار مصدر قبل إضافته للنظام
"""

import sys
from facebook_sources_checker import FacebookSourcesChecker

def test_facebook_url(url):
    """اختبار رابط فيسبوك جديد"""
    
    print("🧪 اختبار مصدر فيسبوك جديد")
    print("=" * 50)
    print(f"🔗 الرابط: {url}")
    
    # إنشاء فاحص المصادر
    checker = FacebookSourcesChecker()
    
    # الحصول على Access Token
    if not checker.get_access_token():
        print("❌ فشل في الحصول على Access Token")
        return False
    
    print(f"✅ تم الحصول على Access Token")
    
    # إنشاء معلومات مصدر مؤقت
    temp_source = {
        "url": url,
        "name": "مصدر تجريبي",
        "category": "اختبار"
    }
    
    # فحص المصدر
    result = checker.check_single_source(temp_source)
    
    # عرض النتائج
    print("\n" + "=" * 50)
    print("📊 نتيجة الاختبار")
    print("=" * 50)
    
    if result["status"] == "نجح كاملاً":
        print("🎉 ممتاز! هذا المصدر يعمل بشكل مثالي")
        print("✅ يمكن إضافته للنظام وسيعمل بدون مشاكل")
        return True
        
    elif result["status"] == "نجح جزئياً":
        print("⚠️ المصدر يعمل جزئياً")
        print("📄 يمكن الوصول لمعلومات الصفحة لكن ليس للمنشورات")
        print("💡 قد تحتاج صلاحيات إضافية من Facebook")
        
        if result.get("recommendations"):
            print("\n🔧 التوصيات:")
            for rec in result["recommendations"]:
                print(f"  • {rec}")
        
        return False
        
    else:
        print("❌ المصدر لا يعمل")
        print("🚫 لا يمكن الوصول للصفحة أو المنشورات")
        
        if result.get("recommendations"):
            print("\n🔧 التوصيات:")
            for rec in result["recommendations"]:
                print(f"  • {rec}")
        
        return False

def suggest_working_sources():
    """اقتراح مصادر قد تعمل"""
    
    print("\n💡 مصادر مقترحة للاختبار:")
    print("=" * 50)
    
    suggested_sources = [
        {
            "name": "BBC News",
            "url": "https://facebook.com/bbcnews",
            "description": "أخبار عالمية باللغة الإنجليزية"
        },
        {
            "name": "CNN",
            "url": "https://facebook.com/cnn",
            "description": "أخبار عالمية باللغة الإنجليزية"
        },
        {
            "name": "Reuters",
            "url": "https://facebook.com/Reuters",
            "description": "وكالة أنباء عالمية"
        },
        {
            "name": "Associated Press",
            "url": "https://facebook.com/APNews",
            "description": "وكالة الأنباء الأمريكية"
        },
        {
            "name": "Sky News",
            "url": "https://facebook.com/skynews",
            "description": "أخبار بريطانية"
        }
    ]
    
    for i, source in enumerate(suggested_sources, 1):
        print(f"{i}. {source['name']}")
        print(f"   🔗 {source['url']}")
        print(f"   📝 {source['description']}")
        print()
    
    print("💡 نصائح لاختيار مصادر تعمل:")
    print("• اختر صفحات أخبار كبيرة ومشهورة")
    print("• تأكد أن الصفحة عامة وليست محمية")
    print("• جرب صفحات باللغة الإنجليزية أولاً")
    print("• تجنب الصفحات الشخصية أو المجموعات")

def interactive_test():
    """اختبار تفاعلي"""
    
    print("🎯 اختبار تفاعلي لمصادر الفيسبوك")
    print("=" * 50)
    
    while True:
        print("\nاختر إحدى الخيارات:")
        print("1. اختبار رابط جديد")
        print("2. عرض مصادر مقترحة")
        print("3. اختبار المصادر المقترحة")
        print("4. خروج")
        
        choice = input("\nأدخل اختيارك (1-4): ").strip()
        
        if choice == "1":
            url = input("\nأدخل رابط صفحة الفيسبوك: ").strip()
            if url:
                test_facebook_url(url)
            else:
                print("❌ يرجى إدخال رابط صحيح")
                
        elif choice == "2":
            suggest_working_sources()
            
        elif choice == "3":
            print("\n🧪 اختبار المصادر المقترحة...")
            suggested_urls = [
                "https://facebook.com/bbcnews",
                "https://facebook.com/cnn",
                "https://facebook.com/Reuters"
            ]
            
            for url in suggested_urls:
                print(f"\n{'='*30}")
                test_facebook_url(url)
                
        elif choice == "4":
            print("👋 وداعاً!")
            break
            
        else:
            print("❌ اختيار غير صحيح")

def main():
    """الدالة الرئيسية"""
    
    if len(sys.argv) > 1:
        # اختبار رابط من سطر الأوامر
        url = sys.argv[1]
        test_facebook_url(url)
    else:
        # وضع تفاعلي
        interactive_test()

if __name__ == "__main__":
    main()
