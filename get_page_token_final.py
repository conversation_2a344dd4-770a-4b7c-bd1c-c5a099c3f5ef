#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الحل النهائي: الحصول على Page Access Token
"""

import webbrowser
import requests
import os
from dotenv import load_dotenv

load_dotenv()

def get_page_token_direct():
    """الحصول على Page Access Token مباشرة"""
    print("🔑 الحل النهائي: الحصول على Page Access Token")
    print("=" * 60)
    
    app_id = os.getenv('FACEBOOK_APP_ID')
    your_page_id = "**************"
    
    print(f"📱 App ID: {app_id}")
    print(f"📄 صفحتك: {your_page_id}")
    print(f"🎯 الهدف: الحصول على Page Access Token")
    
    print(f"\n🔧 **الطريقة الوحيدة التي ستعمل:**")
    print(f"")
    print(f"1️⃣ **افتح Graph API Explorer:**")
    
    # رابط مباشر لـ Graph API Explorer
    explorer_url = f"https://developers.facebook.com/tools/explorer/{app_id}/?method=GET&path=me%2Faccounts&version=v18.0"
    
    try:
        webbrowser.open(explorer_url)
        print(f"   ✅ تم فتح Graph API Explorer")
    except:
        print(f"   🔗 افتح هذا الرابط:")
        print(f"   {explorer_url}")
    
    print(f"\n2️⃣ **احصل على User Token أولاً:**")
    print(f"   • اضغط 'Get Token' > 'Get User Access Token'")
    print(f"   • لا تهتم بالصلاحيات الآن - اضغط 'Generate Access Token'")
    print(f"   • سجل الدخول بحساب Facebook")
    
    print(f"\n3️⃣ **اجلب قائمة صفحاتك:**")
    print(f"   • تأكد أن المسار هو: me/accounts")
    print(f"   • اضغط 'Submit'")
    print(f"   • ستظهر قائمة بصفحاتك")
    
    print(f"\n4️⃣ **ابحث عن صفحتك:**")
    print(f"   • ابحث عن صفحة بـ ID: {your_page_id}")
    print(f"   • أو ابحث عن اسم صفحتك")
    print(f"   • انسخ 'access_token' الخاص بهذه الصفحة")
    
    print(f"\n5️⃣ **إذا لم تظهر صفحتك:**")
    print(f"   • اضغط 'Get Token' > 'Get Page Access Token'")
    print(f"   • اختر صفحتك من القائمة المنسدلة")
    print(f"   • اضغط 'Generate Access Token'")
    print(f"   • انسخ الـ Token")
    
    print(f"\n💡 **ما تبحث عنه:**")
    print(f"   • Page Access Token يبدأ بـ EAAB...")
    print(f"   • مختلف عن User Token")
    print(f"   • خاص بصفحتك (ID: {your_page_id})")
    print(f"   • له صلاحيات أكثر")
    
    return True

def save_page_token_manual():
    """حفظ Page Access Token يدوياً"""
    print(f"\n💾 حفظ Page Access Token")
    print("=" * 60)
    
    print(f"🔑 الصق Page Access Token هنا:")
    page_token = input("Page Token: ").strip()
    
    if not page_token:
        print("❌ لم يتم إدخال Page Token")
        return False
    
    if not page_token.startswith('EAAB'):
        print("⚠️ تحذير: Page Token عادة يبدأ بـ EAAB")
        confirm = input("هل تريد المتابعة؟ (y/n): ").strip().lower()
        if confirm != 'y':
            return False
    
    try:
        # قراءة ملف .env
        env_file = ".env"
        lines = []
        
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        
        # إضافة أو تحديث Page Token
        page_token_line = f"FACEBOOK_PAGE_ACCESS_TOKEN={page_token}\n"
        found = False
        
        for i, line in enumerate(lines):
            if line.startswith('FACEBOOK_PAGE_ACCESS_TOKEN='):
                lines[i] = page_token_line
                found = True
                break
        
        if not found:
            # إضافة بعد User Token
            for i, line in enumerate(lines):
                if line.startswith('FACEBOOK_USER_ACCESS_TOKEN='):
                    lines.insert(i + 1, page_token_line)
                    found = True
                    break
            
            if not found:
                lines.append(page_token_line)
        
        # حفظ الملف
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم حفظ Page Token في ملف .env")
        
        # إعادة تحميل متغيرات البيئة
        load_dotenv(override=True)
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في حفظ Page Token: {e}")
        return False

def test_page_token_final():
    """اختبار Page Access Token النهائي"""
    print(f"\n🧪 اختبار Page Access Token")
    print("=" * 60)
    
    page_token = os.getenv('FACEBOOK_PAGE_ACCESS_TOKEN')
    your_page_id = "**************"
    
    if not page_token:
        print("❌ لا يوجد Page Token للاختبار")
        return False
    
    print(f"🔍 اختبار Page Token...")
    print(f"📄 صفحتك: {your_page_id}")
    
    try:
        # اختبار معلومات الصفحة
        url = "https://graph.facebook.com/v18.0/me"
        params = {
            'access_token': page_token,
            'fields': 'id,name,category,fan_count,about'
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Page Token يعمل!")
            print(f"   📝 اسم الصفحة: {data.get('name', 'غير محدد')}")
            print(f"   🆔 معرف الصفحة: {data.get('id', 'غير محدد')}")
            print(f"   📂 فئة الصفحة: {data.get('category', 'غير محدد')}")
            print(f"   👥 المتابعون: {data.get('fan_count', 0)}")
            
            # التحقق من أن هذا Page Token لصفحتك
            if data.get('id') == your_page_id:
                print(f"✅ هذا Page Token لصفحتك الصحيحة!")
            else:
                print(f"⚠️ هذا Page Token لصفحة أخرى (ID: {data.get('id')})")
            
            # اختبار منشورات الصفحة
            print(f"\n🔍 اختبار منشورات الصفحة...")
            posts_url = "https://graph.facebook.com/v18.0/me/posts"
            posts_params = {
                'access_token': page_token,
                'fields': 'id,message,created_time,type',
                'limit': 5
            }
            
            posts_response = requests.get(posts_url, params=posts_params, timeout=10)
            
            if posts_response.status_code == 200:
                posts_data = posts_response.json()
                posts = posts_data.get('data', [])
                print(f"✅ يمكن الوصول لمنشورات الصفحة! ({len(posts)} منشور)")
                
                if posts:
                    for i, post in enumerate(posts, 1):
                        message = post.get('message', 'بدون نص')
                        if message:
                            preview = message[:50] + "..." if len(message) > 50 else message
                            print(f"   {i}. {preview}")
                else:
                    print(f"   📝 لا توجد منشورات حالياً - أضف منشورات لصفحتك")
                
                # اختبار الوصول لصفحات أخرى
                print(f"\n🔍 اختبار الوصول لصفحات أخرى...")
                test_other_pages(page_token)
                
                return True
                
            else:
                posts_error = posts_response.json() if posts_response.content else {}
                posts_msg = posts_error.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"❌ لا يمكن الوصول لمنشورات الصفحة: {posts_msg}")
                return False
                
        else:
            error_data = response.json() if response.content else {}
            error_msg = error_data.get('error', {}).get('message', 'خطأ غير معروف')
            print(f"❌ Page Token لا يعمل: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Page Token: {e}")
        return False

def test_other_pages(page_token):
    """اختبار الوصول لصفحات أخرى باستخدام Page Token"""
    test_pages = [
        {'id': 'facebook', 'name': 'Facebook'},
        {'id': 'meta', 'name': 'Meta'}
    ]
    
    accessible_pages = []
    
    for page in test_pages:
        try:
            url = f"https://graph.facebook.com/v18.0/{page['id']}"
            params = {
                'access_token': page_token,
                'fields': 'id,name,category'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ {data.get('name', page['name'])}")
                accessible_pages.append(page['id'])
            else:
                print(f"   ❌ {page['name']} - لا يمكن الوصول")
                
        except:
            print(f"   ❌ {page['name']} - خطأ")
    
    if accessible_pages:
        print(f"\n🎉 يمكن الوصول لـ {len(accessible_pages)} صفحة أخرى!")
    else:
        print(f"\n⚠️ يمكن الوصول لصفحتك فقط (هذا طبيعي)")

def final_instructions():
    """التعليمات النهائية"""
    print(f"\n🎉 التعليمات النهائية")
    print("=" * 60)
    
    page_token = os.getenv('FACEBOOK_PAGE_ACCESS_TOKEN')
    
    if page_token:
        print(f"✅ Page Token محفوظ ويعمل!")
        print(f"")
        print(f"🚀 **الآن يمكنك:**")
        print(f"")
        print(f"1️⃣ **شغل التطبيق:**")
        print(f"   python app.py")
        print(f"")
        print(f"2️⃣ **اذهب للواجهة:**")
        print(f"   http://localhost:5020")
        print(f"")
        print(f"3️⃣ **جرب جمع الأخبار:**")
        print(f"   • اذهب لـ 'لوحة التحكم'")
        print(f"   • اضغط 'جمع الأخبار'")
        print(f"   • ستجمع أخبار من صفحتك")
        print(f"")
        print(f"4️⃣ **لتحسين النتائج:**")
        print(f"   • أضف منشورات أخبار لصفحتك")
        print(f"   • شارك روابط أخبار")
        print(f"   • اكتب تحديثات إخبارية")
        
    else:
        print(f"❌ لم يتم حفظ Page Token")
        print(f"🔄 أعد تشغيل هذه الأداة واحصل على Page Token")

def main():
    """الدالة الرئيسية"""
    print("🔑 الحل النهائي للوصول لأخبار Facebook")
    print("=" * 60)
    print("📄 صفحتك: **************")
    print("🎯 الهدف: الحصول على Page Access Token")
    print("=" * 60)
    
    # فتح Graph API Explorer
    get_page_token_direct()
    
    input(f"\n⏸️ اضغط Enter بعد الحصول على Page Token...")
    
    # حفظ Page Token
    if save_page_token_manual():
        # اختبار Page Token
        if test_page_token_final():
            # التعليمات النهائية
            final_instructions()
        else:
            print(f"\n❌ Page Token لا يعمل، جرب مرة أخرى")
    else:
        print(f"\n❌ فشل في حفظ Page Token")
    
    print(f"\n📋 ملخص:")
    print(f"• Page Access Token هو الحل الوحيد")
    print(f"• يمكن الوصول لصفحتك على الأقل")
    print(f"• أضف محتوى أكثر لصفحتك")
    print(f"• شغل التطبيق واختبر النتائج")

if __name__ == "__main__":
    main()
