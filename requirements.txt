# متطلبات مشروع iNews الآمن
# 🔒 مكتبات الأمان والتشفير
cryptography==41.0.7
bcrypt==4.1.2

# 📡 مكتبات الشبكة والAPI
requests==2.31.0
facebook-sdk==3.1.0

# 🌐 مكتبات الويب
flask==3.0.0
flask-limiter==3.5.0

# 📊 مكتبات معالجة البيانات
pandas==2.1.4
beautifulsoup4==4.12.2

# ⚙️ مكتبات الإعدادات والمساعدة
python-dotenv==1.0.0
python-dateutil==2.8.2
pytz==2023.3
schedule==1.2.0

# 🗄️ قاعدة البيانات
sqlalchemy==2.0.23
