# 🔧 دليل حل مشاكل Facebook API

## 📊 نتائج فحص المصادر

### ❌ المشكلة الحالية:
```
نسبة النجاح: 0.0%
المصدر "يلا" لا يعمل
```

### 🔍 سبب المشكلة:
التطبيق يحتاج **صلاحيات إضافية** من Facebook:
- `pages_read_engagement`
- `Page Public Content Access`
- `Page Public Metadata Access`

## 🛠️ الحلول المتاحة

### الحل 1: طلب صلاحيات إضافية من Facebook

#### الخطوات:
1. **اذهب إلى Facebook Developers:**
   - https://developers.facebook.com/apps/1905779223574690

2. **أضف المنتجات المطلوبة:**
   - اذهب إلى "Add Products"
   - <PERSON>ضف "Facebook Login"
   - أضف "Pages API"

3. **اطلب الصلاحيات:**
   - اذهب إلى "App Review"
   - اطلب `pages_read_engagement`
   - اطلب `Page Public Content Access`

4. **املأ نموذج المراجعة:**
   - اشرح أن التطبيق لجمع الأخبار
   - أرفق لقطات شاشة
   - اذكر أنه للاستخدام الشخصي

#### ⚠️ تحذير:
- قد يستغرق الموافقة أسابيع
- قد يتم رفض الطلب
- يتطلب تطبيق مكتمل للمراجعة

### الحل 2: استخدام مصادر أخبار عامة

#### مصادر مقترحة تعمل بدون صلاحيات خاصة:
```
✅ BBC News: https://facebook.com/bbcnews
✅ CNN: https://facebook.com/cnn  
✅ Reuters: https://facebook.com/Reuters
✅ Associated Press: https://facebook.com/APNews
✅ Sky News: https://facebook.com/skynews
```

#### كيفية الاختبار:
```bash
python test_new_source.py https://facebook.com/bbcnews
```

### الحل 3: استخدام Graph API Explorer

#### للاختبار المباشر:
1. اذهب إلى: https://developers.facebook.com/tools/explorer/
2. اختر تطبيقك
3. جرب الاستعلامات:
   ```
   /bbcnews?fields=id,name,category
   /bbcnews/posts?fields=id,message,created_time
   ```

## 🧪 أدوات الاختبار المتاحة

### 1. فحص جميع المصادر:
```bash
python facebook_sources_checker.py
```

### 2. اختبار مصدر جديد:
```bash
python test_new_source.py
```

### 3. اختبار رابط محدد:
```bash
python test_new_source.py "https://facebook.com/bbcnews"
```

## 📋 خطة العمل المقترحة

### المرحلة 1: اختبار مصادر عامة (فوري)
1. **اختبر المصادر المقترحة:**
   ```bash
   python test_new_source.py
   # اختر الخيار 3: اختبار المصادر المقترحة
   ```

2. **أضف المصادر التي تعمل:**
   - اذهب لواجهة الويب
   - أضف المصادر الناجحة
   - اجمع الأخبار

### المرحلة 2: طلب صلاحيات Facebook (طويل المدى)
1. **حضر التطبيق للمراجعة:**
   - أكمل واجهة الويب
   - أضف صفحة سياسة الخصوصية
   - أضف شروط الاستخدام

2. **اطلب المراجعة:**
   - اذهب لـ Facebook App Review
   - اطلب الصلاحيات المطلوبة
   - انتظر الموافقة

### المرحلة 3: تطوير بدائل (احتياطي)
1. **مصادر RSS:**
   - BBC Arabic RSS
   - الجزيرة RSS
   - العربية RSS

2. **Web Scraping:**
   - جمع أخبار من مواقع مباشرة
   - استخدام Beautiful Soup

## 🔍 تشخيص المشاكل

### إذا كان المصدر لا يعمل:

#### تحقق من:
1. **صحة الرابط:**
   ```
   ✅ https://facebook.com/pagename
   ❌ https://facebook.com/profile.php?id=123
   ```

2. **نوع الصفحة:**
   ```
   ✅ صفحة عامة (Page)
   ❌ ملف شخصي (Profile)
   ❌ مجموعة (Group)
   ```

3. **حالة الصفحة:**
   ```
   ✅ نشطة ومحدثة
   ❌ محذوفة أو معطلة
   ```

### رسائل الخطأ الشائعة:

#### `pages_read_engagement permission required`
**الحل:** طلب صلاحية من Facebook

#### `Object does not exist`
**الحل:** تحقق من صحة الرابط

#### `Cannot be loaded due to missing permissions`
**الحل:** استخدم صفحات عامة أكثر

## 📞 الدعم

### إذا احتجت مساعدة:
1. **شغل فحص المصادر:**
   ```bash
   python facebook_sources_checker.py
   ```

2. **راجع التقرير المحفوظ:**
   ```
   facebook_sources_report_YYYYMMDD_HHMMSS.json
   ```

3. **جرب المصادر المقترحة:**
   ```bash
   python test_new_source.py
   ```

---

## 🎯 الخلاصة

**المشكلة:** تطبيق Facebook يحتاج صلاحيات إضافية

**الحل السريع:** استخدم مصادر أخبار عالمية عامة

**الحل طويل المدى:** اطلب صلاحيات من Facebook

**البديل:** استخدم مصادر RSS أو Web Scraping
