"""
إنشاء بيانات تجريبية لاختبار النظام
"""

from datetime import datetime, timedelta
import json
from security import security_manager
from inews_collector import iNewsCollector
import random

def create_sample_news():
    """إنشاء أخبار تجريبية"""
    
    sample_news = []
    
    # أخبار تجريبية
    news_samples = [
        {
            "page_name": "BBC Arabic",
            "page_id": "bbc_arabic",
            "message": "عاجل: تطورات جديدة في الأحداث العالمية اليوم. تابعوا آخر الأخبار والتحديثات المهمة من حول العالم.",
            "type": "status",
            "reactions_count": 1250,
            "comments_count": 89,
            "shares_count": 234
        },
        {
            "page_name": "الجزيرة",
            "page_id": "aljazeera",
            "message": "تقرير خاص: التطورات الاقتصادية الجديدة وتأثيرها على الأسواق العربية. تحليل شامل للوضع الحالي.",
            "type": "link",
            "link": "https://aljazeera.net/news",
            "reactions_count": 2100,
            "comments_count": 156,
            "shares_count": 445
        },
        {
            "page_name": "العربية",
            "page_id": "alarabiya",
            "message": "رياضة: نتائج المباريات اليوم وأهم الأحداث الرياضية. تغطية شاملة لجميع البطولات المحلية والعالمية.",
            "type": "photo",
            "reactions_count": 890,
            "comments_count": 67,
            "shares_count": 123
        },
        {
            "page_name": "سكاي نيوز عربية",
            "page_id": "skynews_arabia",
            "message": "تقنية: إطلاق تقنيات جديدة في مجال الذكاء الاصطناعي. كيف ستؤثر هذه التطورات على مستقبل التكنولوجيا؟",
            "type": "video",
            "reactions_count": 1567,
            "comments_count": 234,
            "shares_count": 567
        },
        {
            "page_name": "CNN بالعربية",
            "page_id": "cnn_arabic",
            "message": "صحة: نصائح مهمة للحفاظ على الصحة في فصل الشتاء. إرشادات من الخبراء للوقاية من الأمراض الموسمية.",
            "type": "status",
            "reactions_count": 756,
            "comments_count": 45,
            "shares_count": 189
        }
    ]
    
    # إنشاء أخبار بتواريخ مختلفة
    for i, news in enumerate(news_samples):
        # تواريخ متنوعة (آخر 7 أيام)
        created_time = datetime.now() - timedelta(days=random.randint(0, 7), 
                                                 hours=random.randint(0, 23),
                                                 minutes=random.randint(0, 59))
        
        news_item = {
            "page_name": news["page_name"],
            "page_id": news["page_id"],
            "post_id": f"{news['page_id']}_{i+1}",
            "message": news["message"],
            "story": "",
            "created_time": created_time.isoformat(),
            "type": news["type"],
            "link": news.get("link", ""),
            "reactions_count": news["reactions_count"],
            "comments_count": news["comments_count"],
            "shares_count": news["shares_count"],
            "collected_at": datetime.now().isoformat()
        }
        
        sample_news.append(news_item)
    
    return sample_news

def create_sample_pages():
    """إنشاء صفحات تجريبية"""
    
    sample_pages = [
        {
            "id": "bbc_arabic",
            "url": "https://facebook.com/bbc.arabic",
            "name": "BBC Arabic",
            "category": "أخبار عالمية",
            "added_at": datetime.now().isoformat(),
            "active": True
        },
        {
            "id": "aljazeera",
            "url": "https://facebook.com/aljazeera",
            "name": "الجزيرة",
            "category": "أخبار عامة",
            "added_at": datetime.now().isoformat(),
            "active": True
        },
        {
            "id": "alarabiya",
            "url": "https://facebook.com/alarabiya",
            "name": "العربية",
            "category": "أخبار عامة",
            "added_at": datetime.now().isoformat(),
            "active": True
        },
        {
            "id": "skynews_arabia",
            "url": "https://facebook.com/skynewsarabia",
            "name": "سكاي نيوز عربية",
            "category": "أخبار عالمية",
            "added_at": datetime.now().isoformat(),
            "active": False
        },
        {
            "id": "cnn_arabic",
            "url": "https://facebook.com/cnnarabic",
            "name": "CNN بالعربية",
            "category": "أخبار عالمية",
            "added_at": datetime.now().isoformat(),
            "active": True
        }
    ]
    
    return sample_pages

def main():
    """إنشاء البيانات التجريبية"""
    
    print("🔧 إنشاء بيانات تجريبية لـ iNews...")
    
    try:
        # إنشاء الأخبار التجريبية
        print("📰 إنشاء أخبار تجريبية...")
        sample_news = create_sample_news()
        
        # حفظ الأخبار بشكل آمن
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        news_filename = f"facebook_news_{timestamp}.json"
        
        news_filepath = security_manager.secure_file_save(
            sample_news, 
            news_filename, 
            encrypt=True
        )
        
        print(f"✅ تم حفظ {len(sample_news)} خبر تجريبي في: {news_filepath}")
        
        # إنشاء الصفحات التجريبية
        print("📄 إنشاء صفحات تجريبية...")
        sample_pages = create_sample_pages()
        
        # حفظ الصفحات
        with open('pages.json', 'w', encoding='utf-8') as f:
            json.dump(sample_pages, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ {len(sample_pages)} صفحة تجريبية في: pages.json")
        
        print("\n🎉 تم إنشاء البيانات التجريبية بنجاح!")
        print("📱 يمكنك الآن تصفح الواجهة ورؤية الأخبار والمصادر")
        print("🔐 كلمة مرور الدخول: YaserAdmin2024!SecureNews@Protection")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات: {e}")

if __name__ == "__main__":
    main()
