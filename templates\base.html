<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}جامع الأخبار من تلغرام{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .news-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .news-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.9em;
        }
        
        .channel-tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="bi bi-telegram text-primary"></i>
                جامع الأخبار من تلغرام
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="bi bi-house"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('news_page') }}">
                            <i class="bi bi-newspaper"></i> الأخبار
                        </a>
                    </li>
                    {% if session.logged_in %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-speedometer2"></i> لوحة التحكم
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if session.logged_in %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                        </a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">
                            <i class="bi bi-box-arrow-in-right"></i> تسجيل الدخول
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="mt-5 py-4 text-center text-white">
        <div class="container">
            <p class="mb-0">
                <i class="bi bi-telegram"></i>
                جامع الأخبار من تلغرام - تطوير ياسر الجبوري
            </p>
            <small class="text-white-50">
                جمع الأخبار تلقائياً من قنوات تلغرام المشترك فيها
            </small>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // تحديث الحالة كل 5 ثوان
        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('collection-status');
                    if (statusElement) {
                        if (data.is_running) {
                            statusElement.innerHTML = '<span class="status-badge bg-success">جاري الجمع...</span>';
                        } else if (data.error_message) {
                            statusElement.innerHTML = '<span class="status-badge bg-danger">خطأ</span>';
                        } else {
                            statusElement.innerHTML = '<span class="status-badge bg-secondary">متوقف</span>';
                        }
                    }
                    
                    // تحديث إحصائيات
                    const totalElement = document.getElementById('total-news');
                    if (totalElement && data.total_news) {
                        totalElement.textContent = data.total_news;
                    }
                    
                    const channelsElement = document.getElementById('channels-count');
                    if (channelsElement && data.channels_count) {
                        channelsElement.textContent = data.channels_count;
                    }
                })
                .catch(error => console.error('خطأ في تحديث الحالة:', error));
        }
        
        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
            setInterval(updateStatus, 5000); // كل 5 ثوان
        });
        
        // دوال مساعدة
        function startCollection() {
            fetch('/api/start_collection', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('خطأ: ' + data.error);
                    } else {
                        alert('تم بدء جمع الأخبار');
                        updateStatus();
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ في بدء جمع الأخبار');
                });
        }
        
        function stopCollection() {
            fetch('/api/stop_collection', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    alert('تم إيقاف جمع الأخبار');
                    updateStatus();
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ في إيقاف جمع الأخبار');
                });
        }
        
        function exportNews(format) {
            fetch(`/api/export/${format}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('خطأ: ' + data.error);
                    } else {
                        alert('تم التصدير بنجاح: ' + data.filename);
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ في التصدير');
                });
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
