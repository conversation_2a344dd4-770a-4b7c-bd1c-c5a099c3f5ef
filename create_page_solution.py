#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الحل النهائي: إنشاء صفحة Facebook والحصول على Page Access Token
"""

import webbrowser
import requests
import os
from dotenv import load_dotenv

load_dotenv()

def step1_create_facebook_page():
    """الخطوة 1: إنشاء صفحة Facebook"""
    print("📄 الخطوة 1: إنشاء صفحة Facebook")
    print("=" * 60)
    
    print("🎯 **لماذا نحتاج صفحة Facebook؟**")
    print("   • Page Access Token له صلاحيات أكثر من User Token")
    print("   • يمكن الوصول لمنشورات الصفحات الأخرى")
    print("   • لا يحتاج موافقة Facebook للصلاحيات الأساسية")
    print("   • مدة صلاحية أطول")
    
    print(f"\n📋 **خطوات إنشاء الصفحة:**")
    print(f"")
    print(f"1️⃣ **اذهب لرابط إنشاء الصفحات:**")
    print(f"   🔗 https://www.facebook.com/pages/create")
    print(f"")
    print(f"2️⃣ **اختر نوع الصفحة:**")
    print(f"   📂 'Business or Brand' أو 'Community or Public Figure'")
    print(f"")
    print(f"3️⃣ **معلومات الصفحة:**")
    print(f"   📝 اسم الصفحة: 'أخبار ياسر' أو 'iNews - جمع الأخبار'")
    print(f"   📂 الفئة: 'News & Media Website' أو 'Media/News Company'")
    print(f"   📝 الوصف: 'صفحة لجمع وعرض الأخبار من مصادر مختلفة'")
    print(f"")
    print(f"4️⃣ **إعداد الصفحة:**")
    print(f"   📸 أضف صورة للصفحة (اختياري)")
    print(f"   📸 أضف صورة غلاف (اختياري)")
    print(f"   📝 أضف معلومات إضافية")
    print(f"")
    print(f"5️⃣ **انشر منشور تجريبي:**")
    print(f"   📝 'مرحباً! هذه صفحة لجمع الأخبار من مصادر مختلفة'")
    print(f"   📤 انشر المنشور")
    
    try:
        print(f"\n🔗 فتح رابط إنشاء الصفحات...")
        webbrowser.open("https://www.facebook.com/pages/create")
        print(f"✅ تم فتح الرابط في المتصفح")
        return True
    except:
        print(f"\n🔗 افتح هذا الرابط يدوياً:")
        print(f"https://www.facebook.com/pages/create")
        return False

def step2_get_page_token():
    """الخطوة 2: الحصول على Page Access Token"""
    print(f"\n📄 الخطوة 2: الحصول على Page Access Token")
    print("=" * 60)
    
    app_id = os.getenv('FACEBOOK_APP_ID')
    
    print(f"🎯 **الهدف:** الحصول على Page Access Token مع صلاحيات كاملة")
    print(f"")
    print(f"📋 **الخطوات في Graph API Explorer:**")
    print(f"")
    print(f"1️⃣ **افتح Graph API Explorer:**")
    
    explorer_url = f"https://developers.facebook.com/tools/explorer/{app_id}/"
    
    try:
        webbrowser.open(explorer_url)
        print(f"   ✅ تم فتح Graph API Explorer")
    except:
        print(f"   🔗 افتح هذا الرابط:")
        print(f"   {explorer_url}")
    
    print(f"")
    print(f"2️⃣ **احصل على User Token مع صلاحيات الصفحات:**")
    print(f"   • اضغط 'Get Token' > 'Get User Access Token'")
    print(f"   • أضف هذه الصلاحيات (إذا ظهرت):")
    print(f"     ✓ manage_pages")
    print(f"     ✓ pages_show_list")
    print(f"     ✓ pages_read_engagement")
    print(f"   • اضغط 'Generate Access Token'")
    print(f"   • سجل الدخول ووافق على الصلاحيات")
    print(f"")
    print(f"3️⃣ **اجلب قائمة صفحاتك:**")
    print(f"   • في حقل 'Path'، اكتب: me/accounts")
    print(f"   • اضغط 'Submit'")
    print(f"   • ستظهر قائمة بصفحاتك مع Page Tokens")
    print(f"")
    print(f"4️⃣ **انسخ Page Access Token:**")
    print(f"   • من النتائج، انسخ 'access_token' للصفحة التي أنشأتها")
    print(f"   • أو اضغط 'Get Token' > 'Get Page Access Token'")
    print(f"   • اختر صفحتك من القائمة")
    print(f"   • انسخ الـ Token الذي يظهر")
    
    print(f"\n💡 **نصائح مهمة:**")
    print(f"   • Page Token يبدأ بـ EAAB...")
    print(f"   • Page Token مختلف عن User Token")
    print(f"   • Page Token له صلاحيات أكثر")
    print(f"   • احفظ Page Token في مكان آمن")

def step3_save_page_token():
    """الخطوة 3: حفظ Page Access Token"""
    print(f"\n💾 الخطوة 3: حفظ Page Access Token")
    print("=" * 60)
    
    print(f"🔑 الصق Page Access Token هنا:")
    page_token = input("Page Token: ").strip()
    
    if not page_token:
        print("❌ لم يتم إدخال Page Token")
        return False
    
    if len(page_token) < 50:
        print("⚠️ تحذير: Page Token قصير، تأكد من صحته")
    
    try:
        # قراءة ملف .env
        env_file = ".env"
        lines = []
        
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        
        # إضافة أو تحديث Page Token
        page_token_line = f"FACEBOOK_PAGE_ACCESS_TOKEN={page_token}\n"
        found = False
        
        for i, line in enumerate(lines):
            if line.startswith('FACEBOOK_PAGE_ACCESS_TOKEN='):
                lines[i] = page_token_line
                found = True
                break
        
        if not found:
            # إضافة بعد User Token
            for i, line in enumerate(lines):
                if line.startswith('FACEBOOK_USER_ACCESS_TOKEN='):
                    lines.insert(i + 1, page_token_line)
                    found = True
                    break
            
            if not found:
                lines.append(page_token_line)
        
        # حفظ الملف
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم حفظ Page Token في ملف .env")
        
        # إعادة تحميل متغيرات البيئة
        load_dotenv(override=True)
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في حفظ Page Token: {e}")
        return False

def step4_test_page_token():
    """الخطوة 4: اختبار Page Access Token"""
    print(f"\n🧪 الخطوة 4: اختبار Page Access Token")
    print("=" * 60)
    
    page_token = os.getenv('FACEBOOK_PAGE_ACCESS_TOKEN')
    
    if not page_token:
        print("❌ لا يوجد Page Token للاختبار")
        return False
    
    print(f"🔍 اختبار Page Token...")
    
    # اختبار معلومات الصفحة
    try:
        url = "https://graph.facebook.com/v18.0/me"
        params = {
            'access_token': page_token,
            'fields': 'id,name,category,fan_count,about'
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ معلومات صفحتك:")
            print(f"   📝 الاسم: {data.get('name', 'غير محدد')}")
            print(f"   🆔 المعرف: {data.get('id', 'غير محدد')}")
            print(f"   📂 الفئة: {data.get('category', 'غير محدد')}")
            print(f"   👥 المتابعون: {data.get('fan_count', 0)}")
            
            # اختبار الوصول لصفحات أخرى
            print(f"\n🔍 اختبار الوصول لصفحات الأخبار...")
            
            test_pages = ['facebook', 'meta']
            accessible_pages = []
            
            for page_id in test_pages:
                try:
                    test_url = f"https://graph.facebook.com/v18.0/{page_id}"
                    test_params = {
                        'access_token': page_token,
                        'fields': 'id,name,category'
                    }
                    
                    test_response = requests.get(test_url, params=test_params, timeout=10)
                    
                    if test_response.status_code == 200:
                        test_data = test_response.json()
                        print(f"   ✅ {test_data.get('name', page_id)}")
                        accessible_pages.append(page_id)
                    else:
                        print(f"   ❌ {page_id} - لا يمكن الوصول")
                        
                except:
                    print(f"   ❌ {page_id} - خطأ في الاتصال")
            
            if accessible_pages:
                print(f"\n🎉 Page Token يعمل! يمكن الوصول لـ {len(accessible_pages)} صفحة")
                return True
            else:
                print(f"\n⚠️ Page Token يعمل جزئياً - صفحتك فقط")
                return True
                
        else:
            error_data = response.json() if response.content else {}
            error_msg = error_data.get('error', {}).get('message', 'خطأ غير معروف')
            print(f"❌ فشل في اختبار Page Token: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Page Token: {e}")
        return False

def step5_update_app():
    """الخطوة 5: تحديث التطبيق لاستخدام Page Token"""
    print(f"\n🔄 الخطوة 5: تحديث التطبيق")
    print("=" * 60)
    
    print(f"✅ تم! الآن يمكنك:")
    print(f"")
    print(f"1️⃣ **شغل التطبيق:**")
    print(f"   🚀 python app.py")
    print(f"   🌐 اذهب لـ: http://localhost:5020")
    print(f"")
    print(f"2️⃣ **جرب جمع الأخبار:**")
    print(f"   • اذهب لـ 'لوحة التحكم'")
    print(f"   • اضغط 'جمع الأخبار'")
    print(f"   • راقب النتائج")
    print(f"")
    print(f"3️⃣ **إذا لم تعمل:**")
    print(f"   • تأكد من Page Token في ملف .env")
    print(f"   • جرب إضافة صفحات أخبار يدوياً")
    print(f"   • راجع سجل الأخطاء")
    
    print(f"\n💡 **نصائح للاستخدام:**")
    print(f"   • Page Token أفضل من User Token")
    print(f"   • يمكن إضافة صفحات أخبار مختلفة")
    print(f"   • احفظ النتائج في ملفات CSV/JSON")
    print(f"   • راقب حدود API لتجنب الحظر")

def main():
    """الدالة الرئيسية - الحل الكامل"""
    print("🎯 الحل النهائي: إنشاء صفحة Facebook + Page Access Token")
    print("=" * 80)
    print("📋 هذا هو الحل الوحيد للوصول لأخبار الصفحات")
    print("=" * 80)
    
    # الخطوة 1: إنشاء صفحة
    step1_create_facebook_page()
    input(f"\n⏸️ اضغط Enter بعد إنشاء الصفحة...")
    
    # الخطوة 2: الحصول على Page Token
    step2_get_page_token()
    input(f"\n⏸️ اضغط Enter بعد الحصول على Page Token...")
    
    # الخطوة 3: حفظ Page Token
    if step3_save_page_token():
        # الخطوة 4: اختبار Page Token
        if step4_test_page_token():
            # الخطوة 5: تحديث التطبيق
            step5_update_app()
            
            print(f"\n🎉 تم بنجاح! التطبيق جاهز للاستخدام")
        else:
            print(f"\n❌ Page Token لا يعمل، جرب مرة أخرى")
    else:
        print(f"\n❌ فشل في حفظ Page Token")
    
    print(f"\n📋 ملخص الحل:")
    print(f"✅ إنشاء صفحة Facebook")
    print(f"✅ الحصول على Page Access Token")
    print(f"✅ حفظ Page Token في .env")
    print(f"✅ اختبار Page Token")
    print(f"✅ تشغيل التطبيق مع صلاحيات كاملة")

if __name__ == "__main__":
    main()
