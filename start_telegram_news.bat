@echo off
chcp 65001 >nul
title جامع الأخبار من تلغرام - Telegram News Collector

echo.
echo ========================================
echo    جامع الأخبار من تلغرام
echo    Telegram News Collector
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من وجود البيئة الافتراضية
if not exist "venv" (
    echo 🔧 إنشاء البيئة الافتراضية...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
    echo ✅ تم إنشاء البيئة الافتراضية
)

REM تفعيل البيئة الافتراضية
echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ فشل في تفعيل البيئة الافتراضية
    pause
    exit /b 1
)

echo ✅ تم تفعيل البيئة الافتراضية

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

REM تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo جرب تشغيل: pip install -r requirements.txt
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات

REM التحقق من وجود ملف .env
if not exist ".env" (
    echo ⚠️ ملف .env غير موجود
    echo سيتم إنشاء ملف .env افتراضي...
    echo.
    
    echo # إعدادات تطبيق جمع الأخبار من تلغرام > .env
    echo # ⚠️ معلومات سرية - لا تشارك هذا الملف مع أحد >> .env
    echo. >> .env
    echo # إعدادات تلغرام API >> .env
    echo # احصل عليها من: https://my.telegram.org/apps >> .env
    echo TELEGRAM_API_ID=YOUR_API_ID_HERE >> .env
    echo TELEGRAM_API_HASH=YOUR_API_HASH_HERE >> .env
    echo TELEGRAM_PHONE_NUMBER=YOUR_PHONE_NUMBER_HERE >> .env
    echo. >> .env
    echo # إعدادات قاعدة البيانات >> .env
    echo DATABASE_URL=sqlite:///telegram_news.db >> .env
    echo. >> .env
    echo # إعدادات الأمان >> .env
    echo SECRET_KEY=TelegramNews_Ultra_Secure_Key_2024_Yaser_Protection >> .env
    echo ADMIN_PASSWORD=yaseraljebori@25m >> .env
    echo. >> .env
    echo # إعدادات التطبيق >> .env
    echo FLASK_ENV=development >> .env
    echo FLASK_DEBUG=True >> .env
    echo PORT=5020 >> .env
    echo. >> .env
    echo # إعدادات جمع الأخبار >> .env
    echo NEWS_UPDATE_INTERVAL=300 >> .env
    echo MAX_MESSAGES_PER_CHANNEL=50 >> .env
    echo AUTO_START_COLLECTION=True >> .env
    
    echo ✅ تم إنشاء ملف .env افتراضي
    echo.
    echo 📝 يرجى تحديث ملف .env ببيانات تلغرام API الخاصة بك:
    echo    1. اذهب إلى: https://my.telegram.org/apps
    echo    2. أنشئ تطبيق جديد واحصل على API ID و API Hash
    echo    3. حدث ملف .env بالبيانات الصحيحة
    echo.
    echo اضغط أي مفتاح للمتابعة...
    pause >nul
)

REM التحقق من التطبيق الرئيسي
if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل التطبيق...
echo.
echo ========================================
echo   معلومات التشغيل:
echo   🌐 الرابط: http://localhost:5020
echo   🔑 كلمة المرور: yaseraljebori@25m
echo   📱 التطبيق: جامع الأخبار من تلغرام
echo ========================================
echo.
echo 💡 نصائح:
echo   • تأكد من إعداد بيانات تلغرام API في ملف .env
echo   • سجل الدخول بكلمة المرور أعلاه
echo   • اذهب للوحة التحكم لبدء جمع الأخبار
echo.
echo ⏹️ لإيقاف التطبيق: اضغط Ctrl+C
echo.

REM تشغيل التطبيق
python app.py

REM في حالة إنهاء التطبيق
echo.
echo 🛑 تم إيقاف التطبيق
echo.
pause
