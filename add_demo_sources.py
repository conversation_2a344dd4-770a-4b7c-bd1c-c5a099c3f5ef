"""
إضافة مصادر تجريبية للنظام
حتى يتم الحصول على صلاحيات Facebook المطلوبة
"""

import json
from datetime import datetime

def add_demo_facebook_sources():
    """إضافة مصادر فيسبوك تجريبية"""
    
    print("📄 إضافة مصادر فيسبوك تجريبية")
    print("=" * 50)
    
    # مصادر تجريبية مع معرفات حقيقية
    demo_sources = [
        {
            "id": "bbcnews",
            "url": "https://facebook.com/bbcnews",
            "name": "BBC News",
            "category": "أخبار عالمية",
            "added_at": datetime.now().isoformat(),
            "active": True,
            "note": "مصدر تجريبي - يحتاج صلاحيات Facebook"
        },
        {
            "id": "cnn",
            "url": "https://facebook.com/cnn",
            "name": "CNN",
            "category": "أخبار عالمية",
            "added_at": datetime.now().isoformat(),
            "active": True,
            "note": "مصدر تجريبي - يحتاج صلاحيات Facebook"
        },
        {
            "id": "reuters",
            "url": "https://facebook.com/Reuters",
            "name": "Reuters",
            "category": "أخبار عالمية",
            "added_at": datetime.now().isoformat(),
            "active": True,
            "note": "مصدر تجريبي - يحتاج صلاحيات Facebook"
        },
        {
            "id": "apnews",
            "url": "https://facebook.com/APNews",
            "name": "Associated Press",
            "category": "أخبار عالمية",
            "added_at": datetime.now().isoformat(),
            "active": True,
            "note": "مصدر تجريبي - يحتاج صلاحيات Facebook"
        },
        {
            "id": "skynews",
            "url": "https://facebook.com/skynews",
            "name": "Sky News",
            "category": "أخبار عالمية",
            "added_at": datetime.now().isoformat(),
            "active": False,  # معطل افتراضياً
            "note": "مصدر تجريبي - يحتاج صلاحيات Facebook"
        }
    ]
    
    # تحميل المصادر الحالية
    try:
        with open('pages.json', 'r', encoding='utf-8') as f:
            current_sources = json.load(f)
    except FileNotFoundError:
        current_sources = []
    
    # إضافة المصادر الجديدة (تجنب التكرار)
    existing_ids = {source.get('id') for source in current_sources}
    new_sources = []
    
    for source in demo_sources:
        if source['id'] not in existing_ids:
            new_sources.append(source)
            print(f"✅ إضافة: {source['name']} ({source['id']})")
        else:
            print(f"⚠️ موجود مسبقاً: {source['name']} ({source['id']})")
    
    # دمج المصادر
    all_sources = current_sources + new_sources
    
    # حفظ المصادر المحدثة
    with open('pages.json', 'w', encoding='utf-8') as f:
        json.dump(all_sources, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 تم إضافة {len(new_sources)} مصدر جديد")
    print(f"📊 إجمالي المصادر: {len(all_sources)}")
    
    return len(new_sources)

def create_demo_news_data():
    """إنشاء بيانات أخبار تجريبية"""
    
    print("\n📰 إنشاء بيانات أخبار تجريبية")
    print("=" * 50)
    
    from datetime import timedelta
    import random
    from security import security_manager
    
    # أخبار تجريبية واقعية
    demo_news = [
        {
            "page_name": "BBC News",
            "page_id": "bbcnews",
            "post_id": "bbcnews_1",
            "message": "Breaking: Major developments in global politics as world leaders gather for emergency summit",
            "story": "",
            "created_time": (datetime.now() - timedelta(hours=2)).isoformat(),
            "type": "status",
            "link": "",
            "reactions_count": 1250,
            "comments_count": 89,
            "shares_count": 234,
            "collected_at": datetime.now().isoformat()
        },
        {
            "page_name": "CNN",
            "page_id": "cnn",
            "post_id": "cnn_1",
            "message": "LIVE: Economic markets show significant movement following latest policy announcements",
            "story": "",
            "created_time": (datetime.now() - timedelta(hours=4)).isoformat(),
            "type": "link",
            "link": "https://cnn.com/news",
            "reactions_count": 2100,
            "comments_count": 156,
            "shares_count": 445,
            "collected_at": datetime.now().isoformat()
        },
        {
            "page_name": "Reuters",
            "page_id": "reuters",
            "post_id": "reuters_1",
            "message": "Technology sector sees major breakthrough in artificial intelligence applications",
            "story": "",
            "created_time": (datetime.now() - timedelta(hours=6)).isoformat(),
            "type": "photo",
            "link": "",
            "reactions_count": 890,
            "comments_count": 67,
            "shares_count": 123,
            "collected_at": datetime.now().isoformat()
        },
        {
            "page_name": "Associated Press",
            "page_id": "apnews",
            "post_id": "apnews_1",
            "message": "Health officials announce new guidelines for public safety measures",
            "story": "",
            "created_time": (datetime.now() - timedelta(hours=8)).isoformat(),
            "type": "status",
            "link": "",
            "reactions_count": 756,
            "comments_count": 45,
            "shares_count": 189,
            "collected_at": datetime.now().isoformat()
        },
        {
            "page_name": "BBC News",
            "page_id": "bbcnews",
            "post_id": "bbcnews_2",
            "message": "Sports: International championship results and upcoming tournament schedules",
            "story": "",
            "created_time": (datetime.now() - timedelta(hours=12)).isoformat(),
            "type": "video",
            "link": "",
            "reactions_count": 1567,
            "comments_count": 234,
            "shares_count": 567,
            "collected_at": datetime.now().isoformat()
        },
        {
            "page_name": "CNN",
            "page_id": "cnn",
            "post_id": "cnn_2",
            "message": "Weather: Severe weather patterns affecting multiple regions worldwide",
            "story": "",
            "created_time": (datetime.now() - timedelta(days=1)).isoformat(),
            "type": "status",
            "link": "",
            "reactions_count": 945,
            "comments_count": 78,
            "shares_count": 267,
            "collected_at": datetime.now().isoformat()
        },
        {
            "page_name": "Reuters",
            "page_id": "reuters",
            "post_id": "reuters_2",
            "message": "Business: Major corporations announce quarterly earnings and future projections",
            "story": "",
            "created_time": (datetime.now() - timedelta(days=1, hours=6)).isoformat(),
            "type": "link",
            "link": "https://reuters.com/business",
            "reactions_count": 1123,
            "comments_count": 92,
            "shares_count": 334,
            "collected_at": datetime.now().isoformat()
        },
        {
            "page_name": "Associated Press",
            "page_id": "apnews",
            "post_id": "apnews_2",
            "message": "Science: Researchers make significant discovery in renewable energy technology",
            "story": "",
            "created_time": (datetime.now() - timedelta(days=2)).isoformat(),
            "type": "photo",
            "link": "",
            "reactions_count": 2234,
            "comments_count": 167,
            "shares_count": 789,
            "collected_at": datetime.now().isoformat()
        }
    ]
    
    # حفظ البيانات بشكل آمن
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"facebook_news_{timestamp}.json"
    
    try:
        filepath = security_manager.secure_file_save(demo_news, filename, encrypt=True)
        print(f"✅ تم حفظ {len(demo_news)} خبر تجريبي في: {filepath}")
        return len(demo_news)
    except Exception as e:
        print(f"❌ فشل في حفظ الأخبار: {e}")
        return 0

def main():
    """الدالة الرئيسية"""
    
    print("🚀 إعداد مصادر وأخبار تجريبية")
    print("=" * 60)
    
    # إضافة المصادر
    sources_added = add_demo_facebook_sources()
    
    # إنشاء الأخبار
    news_added = create_demo_news_data()
    
    print("\n" + "=" * 60)
    print("📊 ملخص العملية")
    print("=" * 60)
    print(f"📄 مصادر مضافة: {sources_added}")
    print(f"📰 أخبار منشأة: {news_added}")
    
    if sources_added > 0 or news_added > 0:
        print("\n✅ تم الإعداد بنجاح!")
        print("\n📱 يمكنك الآن:")
        print("1. فتح واجهة الويب: http://localhost:5000")
        print("2. تسجيل الدخول لإدارة المصادر")
        print("3. مشاهدة الأخبار في الصفحة الرئيسية")
        
        print("\n⚠️ ملاحظة:")
        print("• هذه مصادر وأخبار تجريبية")
        print("• لجمع أخبار حقيقية من Facebook تحتاج صلاحيات إضافية")
        print("• راجع ملف FACEBOOK_API_GUIDE.md للتفاصيل")
    else:
        print("\n⚠️ لم يتم إضافة أي بيانات جديدة")

if __name__ == "__main__":
    main()
