# 📱 iNews - نظام جمع الأخبار الآمن

## 🎯 نظرة عامة
**iNews** هو نظام متكامل وآمن لجمع وعرض الأخبار من صفحات الفيسبوك، مع واجهة ويب سهلة الاستخدام ونظام أمان متقدم.

## ✨ الميزات الرئيسية

### 🔒 الأمان
- **تشفير البيانات**: جميع الملفات الحساسة مشفرة
- **حماية من الهجمات**: منع محاولات الدخول المتكررة
- **مصادقة قوية**: كلمة مرور معقدة للمدير
- **تسجيل العمليات**: جميع الأنشطة مسجلة

### 📰 جمع الأخبار
- **Facebook API**: جمع الأخبار من صفحات الفيسبوك
- **إدارة المصادر**: إضافة وإدارة مصادر متعددة
- **حفظ آمن**: تشفير وحفظ البيانات بأمان
- **تحديث مباشر**: جمع الأخبار في الوقت الفعلي

### 🌐 واجهة الويب
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **بحث وفلترة**: البحث في الأخبار وفلترتها
- **إحصائيات مباشرة**: عرض إحصائيات الأخبار
- **لوحة تحكم**: إدارة شاملة للنظام

## 🚀 التشغيل السريع

### 1. تشغيل التطبيق
```bash
# الطريقة الأولى: استخدام ملف التشغيل
start_inews.bat

# الطريقة الثانية: تشغيل مباشر
python app.py
```

### 2. فتح المتصفح
اذهب إلى: **http://localhost:5020**

### 3. تسجيل الدخول
- **كلمة المرور**: `YaserAdmin2024!SecureNews@Protection`

## 📋 المتطلبات

### المكتبات المطلوبة
```
flask==3.0.0
cryptography==41.0.7
requests==2.31.0
pandas==2.1.4
facebook-sdk==3.1.0
feedparser
python-dotenv==1.0.0
```

### تثبيت المتطلبات
```bash
pip install -r requirements.txt
pip install feedparser
```

## 📁 هيكل المشروع

```
📁 iNews/
├── 📄 app.py                    # التطبيق الرئيسي
├── 📄 inews_collector.py        # جامع أخبار Facebook
├── 📄 security.py               # نظام الأمان
├── 📄 .env                      # الإعدادات السرية
├── 📄 pages.json                # قائمة المصادر
├── 📄 start_inews.bat           # ملف التشغيل السريع
├── 📁 secure_data/              # البيانات المشفرة
├── 📁 templates/                # قوالب HTML
│   ├── base.html
│   ├── dashboard.html
│   ├── login.html
│   └── news.html
├── 📁 static/                   # ملفات CSS/JS
│   └── style.css
└── 📄 *.log                     # ملفات السجلات
```

## 🔧 الاستخدام

### إضافة مصادر الأخبار
1. اذهب إلى لوحة التحكم: `/dashboard`
2. سجل دخولك بكلمة المرور
3. أدخل رابط صفحة الفيسبوك
4. اختر التصنيف واضغط "إضافة المصدر"

### جمع الأخبار
1. في لوحة التحكم، اختر عدد المنشورات
2. اضغط "جمع الأخبار الآن"
3. انتظر حتى اكتمال العملية

### عرض الأخبار
1. اذهب إلى الصفحة الرئيسية: `/`
2. تصفح الأخبار المعروضة
3. استخدم البحث والفلترة

## 🔐 الإعدادات الأمنية

### ملف .env
```env
FACEBOOK_APP_ID=your_app_id
FACEBOOK_APP_SECRET=your_app_secret
SECURITY_KEY=your_security_key
ADMIN_PASSWORD=your_admin_password
```

### نصائح أمنية
- **لا تشارك ملف .env مع أحد**
- **غير كلمة المرور الافتراضية**
- **راجع ملفات السجلات دورياً**
- **احتفظ بنسخة احتياطية من البيانات**

## 🛠️ حل المشاكل

### مشكلة: "لم يتم جمع أي أخبار"
- تأكد من صحة بيانات Facebook API
- تحقق من صلاحيات التطبيق
- جرب صفحات أخبار عامة

### مشكلة: "خطأ في تسجيل الدخول"
- تأكد من كلمة المرور الصحيحة
- انتظر 5 دقائق إذا تم حظر IP
- أعد تشغيل التطبيق

### مشكلة: "لا تظهر الأخبار"
- اذهب إلى لوحة التحكم
- اضغط "جمع الأخبار الآن"
- ارجع للصفحة الرئيسية واضغط "تحديث"

## 📊 الإحصائيات

يعرض النظام إحصائيات مباشرة تشمل:
- **إجمالي الأخبار**: العدد الكلي للأخبار المجمعة
- **أخبار اليوم**: الأخبار المجمعة اليوم
- **المصادر النشطة**: عدد المصادر المفعلة

## 🔄 التحديثات التلقائية

يمكن إعداد النظام لجمع الأخبار تلقائياً:
```python
# في ملف منفصل للمهام المجدولة
import schedule
import time

def collect_news_job():
    # كود جمع الأخبار
    pass

schedule.every(30).minutes.do(collect_news_job)
```

## 📞 الدعم

### في حالة وجود مشاكل:
1. راجع ملف `security.log`
2. راجع ملف `inews.log`
3. أعد تشغيل التطبيق
4. تواصل مع المطور

### معلومات الاتصال:
- **البريد الإلكتروني**: <EMAIL>

---

**🎉 استمتع باستخدام iNews - نظامك الآمن لجمع الأخبار!**
