# 📱 جامع الأخبار من تلغرام

تطبيق ويب متقدم لجمع الأخبار تلقائياً من قنوات تلغرام التي اشتركت فيها.

## ✨ المميزات

- 🔄 **جمع تلقائي**: جمع الأخبار من قنوات تلغرام تلقائياً
- 🎯 **فلترة ذكية**: فلترة المحتوى الإخباري باستخدام كلمات مفتاحية
- 📊 **واجهة ويب جميلة**: واجهة مستخدم عصرية وسهلة الاستخدام
- 📈 **إحصائيات مفصلة**: عرض إحصائيات شاملة للأخبار المجمعة
- 💾 **تصدير متعدد**: تصدير النتائج بصيغ CSV و JSON
- 🔒 **أمان عالي**: حماية البيانات والجلسات
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام

- Python 3.8 أو أحدث
- حساب تلغرام نشط
- اتصال إنترنت مستقر

### 2. تحميل المشروع

```bash
git clone https://github.com/your-username/telegram-news-collector.git
cd telegram-news-collector
```

### 3. إنشاء البيئة الافتراضية

```bash
python -m venv venv
```

**تفعيل البيئة الافتراضية:**

**Windows:**
```bash
venv\Scripts\activate
```

**Linux/Mac:**
```bash
source venv/bin/activate
```

### 4. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 5. إعداد بيانات تلغرام API

1. اذهب إلى [my.telegram.org/apps](https://my.telegram.org/apps)
2. سجل الدخول برقم هاتفك
3. أنشئ تطبيق جديد:
   - **App title**: Telegram News Collector
   - **Short name**: news_collector
   - **Platform**: Desktop
   - **Description**: News collection from Telegram channels

4. احفظ `api_id` و `api_hash`

### 6. تحديث ملف .env

```env
# إعدادات تلغرام API
TELEGRAM_API_ID=YOUR_API_ID_HERE
TELEGRAM_API_HASH=YOUR_API_HASH_HERE
TELEGRAM_PHONE_NUMBER=YOUR_PHONE_NUMBER_HERE

# إعدادات الأمان
ADMIN_PASSWORD=yaseraljebori@25m

# إعدادات التطبيق
PORT=5020
```

## 🎯 الاستخدام

### 1. تشغيل التطبيق

```bash
python app.py
```

### 2. فتح الواجهة

اذهب إلى: [http://localhost:5020](http://localhost:5020)

### 3. تسجيل الدخول

- كلمة المرور الافتراضية: `yaseraljebori@25m`
- يمكن تغييرها في ملف `.env`

### 4. بدء جمع الأخبار

1. اذهب للوحة التحكم
2. اضغط "بدء جمع الأخبار"
3. راقب النتائج في الوقت الفعلي

## 📋 الواجهات المتاحة

### الصفحة الرئيسية
- عرض حالة النظام
- آخر الأخبار
- معلومات الاستخدام

### لوحة التحكم
- التحكم في عملية الجمع
- عرض الإحصائيات
- إدارة القنوات
- تصدير البيانات

### صفحة الأخبار
- عرض جميع الأخبار
- فلترة وبحث متقدم
- ترتيب حسب معايير مختلفة

### صفحة الإعداد
- إرشادات الإعداد
- اختبار الاتصال
- نصائح الأمان

## 🔧 الإعدادات المتقدمة

### متغيرات البيئة (.env)

```env
# إعدادات تلغرام
TELEGRAM_API_ID=YOUR_API_ID
TELEGRAM_API_HASH=YOUR_API_HASH
TELEGRAM_PHONE_NUMBER=+964xxxxxxxxx

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///telegram_news.db

# إعدادات الأمان
SECRET_KEY=your_secret_key
ADMIN_PASSWORD=your_admin_password

# إعدادات التطبيق
FLASK_ENV=development
FLASK_DEBUG=True
PORT=5020

# إعدادات جمع الأخبار
NEWS_UPDATE_INTERVAL=300  # 5 دقائق
MAX_MESSAGES_PER_CHANNEL=50
AUTO_START_COLLECTION=True
```

### تخصيص الفلترة

يمكن تعديل كلمات الفلترة في ملف `telegram_news_collector.py`:

```python
self.news_keywords = [
    'خبر', 'أخبار', 'عاجل', 'breaking', 'news', 'urgent',
    'تطورات', 'مستجدات', 'تقرير', 'حدث', 'واقعة'
]
```

## 📊 تصدير البيانات

### تصدير CSV
- مناسب للتحليل في Excel
- يحتوي على جميع البيانات المهيكلة

### تصدير JSON
- مناسب للمطورين
- يحافظ على هيكل البيانات الأصلي

## 🔒 الأمان

### حماية البيانات
- تشفير جلسات المستخدمين
- حماية بيانات API
- تسجيل آمن للعمليات

### نصائح الأمان
- لا تشارك بيانات API مع أحد
- غير كلمة مرور الإدارة
- احفظ نسخة احتياطية من `.env`
- لا تنشر ملف `.env` على GitHub

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

**خطأ في الاتصال بتلغرام:**
- تأكد من صحة API ID و API Hash
- تأكد من رقم الهاتف بالصيغة الدولية
- تأكد من اتصال الإنترنت

**لا توجد أخبار:**
- تأكد من وجود قنوات إخبارية في اشتراكاتك
- جرب زيادة فترة الجمع (hours_back)
- تحقق من كلمات الفلترة

**مشاكل في الواجهة:**
- امسح cache المتصفح
- تأكد من تشغيل التطبيق على المنفذ الصحيح
- تحقق من سجل الأخطاء

### سجل الأخطاء

يتم حفظ السجلات في:
- `telegram_news.log` - سجل عمليات تلغرام
- `security.log` - سجل الأمان (إن وجد)

## 📈 التطوير

### هيكل المشروع

```
telegram-news-collector/
├── app.py                      # التطبيق الرئيسي
├── telegram_news_collector.py  # جامع الأخبار
├── requirements.txt            # المتطلبات
├── .env                       # متغيرات البيئة
├── README.md                  # هذا الملف
├── templates/                 # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── dashboard.html
│   ├── news.html
│   ├── login.html
│   └── setup.html
├── static/                    # ملفات CSS/JS
│   └── style.css
└── venv/                      # البيئة الافتراضية
```

### إضافة مميزات جديدة

1. **إضافة قنوات مخصصة**
2. **فلترة متقدمة بالذكاء الاصطناعي**
3. **إشعارات فورية**
4. **تحليل المشاعر**
5. **ربط مع قواعد بيانات خارجية**

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للـ branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 المطور

**ياسر الجبوري**
- تطوير وتصميم التطبيق
- إدارة المشروع

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات:

- افتح Issue في GitHub
- راسل المطور مباشرة
- راجع الوثائق الرسمية لتلغرام

## 🔄 التحديثات

### الإصدار 1.0.0
- إطلاق أولي للتطبيق
- جمع الأخبار من تلغرام
- واجهة ويب كاملة
- تصدير البيانات
- نظام أمان متقدم

---

**⭐ إذا أعجبك المشروع، لا تنس إعطاؤه نجمة على GitHub!**
