#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع لتطبيق جمع الأخبار من تلغرام
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def check_venv():
    """التحقق من البيئة الافتراضية وإنشاؤها إذا لم تكن موجودة"""
    venv_path = Path("venv")
    
    if not venv_path.exists():
        print("🔧 إنشاء البيئة الافتراضية...")
        try:
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
            print("✅ تم إنشاء البيئة الافتراضية")
        except subprocess.CalledProcessError:
            print("❌ فشل في إنشاء البيئة الافتراضية")
            return False
    
    return True

def get_python_executable():
    """الحصول على مسار Python في البيئة الافتراضية"""
    if os.name == 'nt':  # Windows
        return Path("venv/Scripts/python.exe")
    else:  # Linux/Mac
        return Path("venv/bin/python")

def install_requirements():
    """تثبيت المتطلبات"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    python_exe = get_python_executable()
    
    if not python_exe.exists():
        print("❌ Python غير موجود في البيئة الافتراضية")
        return False
    
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.run([
            str(python_exe), "-m", "pip", "install", 
            "-r", "requirements.txt", "--quiet"
        ], check=True)
        print("✅ تم تثبيت المتطلبات")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def check_env_file():
    """التحقق من ملف .env"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("⚠️ ملف .env غير موجود")
        print("🔧 تشغيل أداة الإعداد...")
        
        python_exe = get_python_executable()
        
        try:
            subprocess.run([str(python_exe), "setup_telegram_api.py"], check=True)
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تشغيل أداة الإعداد")
            return False
        except FileNotFoundError:
            print("❌ ملف setup_telegram_api.py غير موجود")
            return False
    
    # فحص محتوى ملف .env
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'YOUR_API_ID_HERE' in content or 'YOUR_API_HASH_HERE' in content:
            print("⚠️ ملف .env يحتوي على قيم افتراضية")
            print("🔧 يرجى تحديث ملف .env ببيانات تلغرام API الصحيحة")
            
            choice = input("هل تريد تشغيل أداة الإعداد؟ (y/n): ").strip().lower()
            if choice == 'y':
                python_exe = get_python_executable()
                try:
                    subprocess.run([str(python_exe), "setup_telegram_api.py"], check=True)
                except subprocess.CalledProcessError:
                    print("❌ فشل في تشغيل أداة الإعداد")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف .env: {e}")
        return False

def run_app():
    """تشغيل التطبيق"""
    app_file = Path("app.py")
    
    if not app_file.exists():
        print("❌ ملف app.py غير موجود")
        return False
    
    python_exe = get_python_executable()
    
    print("🚀 بدء تشغيل التطبيق...")
    print("=" * 50)
    print("🌐 الرابط: http://localhost:5020")
    print("🔑 كلمة المرور: yaseraljebori@25m")
    print("📱 التطبيق: جامع الأخبار من تلغرام")
    print("=" * 50)
    print("⏹️ لإيقاف التطبيق: اضغط Ctrl+C")
    print()
    
    try:
        subprocess.run([str(python_exe), "app.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تشغيل التطبيق")
        return False
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق")
        return True

def main():
    """الدالة الرئيسية"""
    print("🚀 تطبيق جمع الأخبار من تلغرام")
    print("=" * 50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من البيئة الافتراضية
    if not check_venv():
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من ملف .env
    if not check_env_file():
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل التطبيق
    run_app()
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 تم إلغاء التشغيل")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
