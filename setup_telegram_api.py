#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إعداد سريع لبيانات تلغرام API
"""

import os
import webbrowser
from dotenv import load_dotenv

def main():
    """الدالة الرئيسية لإعداد بيانات تلغرام API"""
    print("🔧 أداة إعداد بيانات تلغرام API")
    print("=" * 50)
    print("🎯 هذه الأداة ستساعدك في إعداد بيانات تلغرام API")
    print("=" * 50)
    
    # التحقق من وجود ملف .env
    env_exists = os.path.exists('.env')
    
    if env_exists:
        print("✅ تم العثور على ملف .env")
        load_dotenv()
        
        # فحص البيانات الحالية
        api_id = os.getenv('TELEGRAM_API_ID')
        api_hash = os.getenv('TELEGRAM_API_HASH')
        phone = os.getenv('TELEGRAM_PHONE_NUMBER')
        
        if api_id and api_id != 'YOUR_API_ID_HERE':
            print(f"📱 API ID الحالي: {api_id}")
        else:
            print("⚠️ API ID غير محدد")
            
        if api_hash and api_hash != 'YOUR_API_HASH_HERE':
            print(f"🔑 API Hash: {api_hash[:10]}...")
        else:
            print("⚠️ API Hash غير محدد")
            
        if phone and phone != 'YOUR_PHONE_NUMBER_HERE':
            print(f"📞 رقم الهاتف: {phone}")
        else:
            print("⚠️ رقم الهاتف غير محدد")
        
        print()
        choice = input("هل تريد تحديث البيانات؟ (y/n): ").strip().lower()
        if choice != 'y':
            print("تم الإلغاء")
            return
    
    else:
        print("⚠️ ملف .env غير موجود - سيتم إنشاؤه")
    
    print("\n📋 خطوات الحصول على بيانات تلغرام API:")
    print("=" * 50)
    
    print("1️⃣ فتح موقع تلغرام للمطورين...")
    try:
        webbrowser.open("https://my.telegram.org/apps")
        print("✅ تم فتح الموقع في المتصفح")
    except:
        print("🔗 افتح هذا الرابط يدوياً: https://my.telegram.org/apps")
    
    print("\n2️⃣ اتبع هذه الخطوات في الموقع:")
    print("   • سجل الدخول برقم هاتفك المرتبط بتلغرام")
    print("   • اضغط 'Create new application'")
    print("   • املأ النموذج:")
    print("     - App title: Telegram News Collector")
    print("     - Short name: news_collector")
    print("     - Platform: Desktop")
    print("     - Description: News collection from Telegram channels")
    print("   • احفظ api_id و api_hash")
    
    input("\n⏸️ اضغط Enter بعد الحصول على البيانات...")
    
    print("\n📝 إدخال البيانات:")
    print("=" * 50)
    
    # إدخال API ID
    while True:
        api_id = input("🔢 أدخل API ID: ").strip()
        if api_id and api_id.isdigit():
            break
        print("❌ API ID يجب أن يكون رقماً")
    
    # إدخال API Hash
    while True:
        api_hash = input("🔑 أدخل API Hash: ").strip()
        if api_hash and len(api_hash) >= 32:
            break
        print("❌ API Hash يجب أن يكون 32 حرف على الأقل")
    
    # إدخال رقم الهاتف
    while True:
        phone = input("📞 أدخل رقم الهاتف (مثل: +964xxxxxxxxx): ").strip()
        if phone and phone.startswith('+') and len(phone) >= 10:
            break
        print("❌ رقم الهاتف يجب أن يبدأ بـ + ويكون بالصيغة الدولية")
    
    # حفظ البيانات في ملف .env
    print("\n💾 حفظ البيانات...")
    
    env_content = f"""# إعدادات تطبيق جمع الأخبار من تلغرام
# ⚠️ معلومات سرية - لا تشارك هذا الملف مع أحد

# إعدادات تلغرام API
# احصل عليها من: https://my.telegram.org/apps
TELEGRAM_API_ID={api_id}
TELEGRAM_API_HASH={api_hash}
TELEGRAM_PHONE_NUMBER={phone}

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///telegram_news.db

# إعدادات الأمان
SECRET_KEY=TelegramNews_Ultra_Secure_Key_2024_Yaser_Protection
ADMIN_PASSWORD=yaseraljebori@25m

# إعدادات التطبيق
FLASK_ENV=development
FLASK_DEBUG=True
PORT=5020

# إعدادات جمع الأخبار
NEWS_UPDATE_INTERVAL=300  # 5 دقائق
MAX_MESSAGES_PER_CHANNEL=50
AUTO_START_COLLECTION=True

# سيتم جلب القنوات من اشتراكاتك تلقائياً
# لا حاجة لإدخال قنوات يدوياً
"""
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("✅ تم حفظ البيانات في ملف .env")
        
        # اختبار البيانات
        print("\n🧪 اختبار البيانات...")
        
        try:
            from telethon import TelegramClient
            
            # إنشاء عميل تلغرام للاختبار
            client = TelegramClient('test_session', int(api_id), api_hash)
            
            print("🔄 محاولة الاتصال بتلغرام...")
            
            import asyncio
            
            async def test_connection():
                try:
                    await client.start(phone=phone)
                    me = await client.get_me()
                    print(f"✅ تم الاتصال بنجاح! مرحباً {me.first_name}")
                    await client.disconnect()
                    return True
                except Exception as e:
                    print(f"❌ فشل في الاتصال: {e}")
                    await client.disconnect()
                    return False
            
            # تشغيل الاختبار
            success = asyncio.run(test_connection())
            
            if success:
                print("\n🎉 تم الإعداد بنجاح!")
                print("🚀 يمكنك الآن تشغيل التطبيق:")
                print("   • Windows: start_telegram_news.bat")
                print("   • Linux/Mac: python app.py")
                print("   • أو: python app.py")
                
                # حذف ملف الجلسة التجريبي
                try:
                    os.remove('test_session.session')
                except:
                    pass
            else:
                print("\n❌ فشل في الاختبار")
                print("🔧 تحقق من البيانات وأعد المحاولة")
                
        except ImportError:
            print("⚠️ لا يمكن اختبار الاتصال - مكتبة telethon غير مثبتة")
            print("💡 شغل: pip install -r requirements.txt")
            print("✅ تم حفظ البيانات، يمكنك تشغيل التطبيق")
        
    except Exception as e:
        print(f"❌ فشل في حفظ البيانات: {e}")
        return
    
    print("\n📋 الخطوات التالية:")
    print("=" * 50)
    print("1. شغل التطبيق: python app.py")
    print("2. اذهب لـ: http://localhost:5020")
    print("3. سجل الدخول بكلمة المرور: yaseraljebori@25m")
    print("4. اذهب للوحة التحكم وابدأ جمع الأخبار")
    
    print("\n🔒 نصائح الأمان:")
    print("• لا تشارك ملف .env مع أحد")
    print("• غير كلمة مرور الإدارة")
    print("• احفظ نسخة احتياطية من البيانات")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إلغاء الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
