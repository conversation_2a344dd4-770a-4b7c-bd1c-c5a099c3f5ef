"""
🔒 اختبار شامل لنظام الأمان
فحص جميع ميزات الحماية في iNews
"""

import os
import json
from datetime import datetime
from security import security_manager, SecurityError
from inews_collector import iNewsCollector

def test_security_system():
    """اختبار شامل لنظام الأمان"""
    
    print("🔍 بدء اختبار نظام الأمان...")
    print("=" * 50)
    
    # اختبار 1: تحميل الإعدادات
    print("\n1️⃣ اختبار تحميل الإعدادات الآمنة...")
    try:
        app_id, app_secret = security_manager.get_facebook_credentials()
        print(f"✅ App ID: {app_id}")
        print(f"✅ App Secret: {app_secret[:10]}...")
    except Exception as e:
        print(f"❌ فشل: {e}")
        return False
    
    # اختبار 2: التشفير وفك التشفير
    print("\n2️⃣ اختبار التشفير...")
    test_data = "هذه بيانات سرية للاختبار"
    try:
        encrypted = security_manager.encrypt_data(test_data)
        decrypted = security_manager.decrypt_data(encrypted)
        
        if decrypted == test_data:
            print("✅ التشفير وفك التشفير يعمل بشكل صحيح")
        else:
            print("❌ فشل في التشفير/فك التشفير")
            return False
    except Exception as e:
        print(f"❌ خطأ في التشفير: {e}")
        return False
    
    # اختبار 3: حماية كلمات المرور
    print("\n3️⃣ اختبار حماية كلمات المرور...")
    try:
        test_password = "test123"
        hashed = security_manager.hash_password(test_password)
        
        if security_manager.verify_password(test_password, hashed):
            print("✅ تشفير والتحقق من كلمات المرور يعمل")
        else:
            print("❌ فشل في التحقق من كلمة المرور")
            return False
    except Exception as e:
        print(f"❌ خطأ في كلمات المرور: {e}")
        return False
    
    # اختبار 4: الحفظ الآمن
    print("\n4️⃣ اختبار الحفظ الآمن...")
    try:
        test_data = {"test": "بيانات تجريبية", "timestamp": datetime.now().isoformat()}
        filename = "test_secure_file.json"
        
        # حفظ آمن
        filepath = security_manager.secure_file_save(test_data, filename, encrypt=True)
        print(f"✅ تم الحفظ الآمن: {filepath}")
        
        # تحميل آمن
        loaded_data = security_manager.secure_file_load(filename, decrypt=True)
        
        if loaded_data == test_data:
            print("✅ التحميل الآمن يعمل بشكل صحيح")
        else:
            print("❌ فشل في التحميل الآمن")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الحفظ الآمن: {e}")
        return False
    
    # اختبار 5: حماية من محاولات الدخول المتكررة
    print("\n5️⃣ اختبار حماية Rate Limiting...")
    try:
        test_ip = "*************"
        
        # محاولات فاشلة متعددة
        for i in range(4):
            try:
                security_manager.authenticate_admin("wrong_password", test_ip)
            except SecurityError:
                pass
        
        # المحاولة الخامسة يجب أن تكون محظورة
        try:
            security_manager.authenticate_admin("wrong_password", test_ip)
            print("❌ فشل: لم يتم حظر IP بعد المحاولات المتكررة")
            return False
        except SecurityError as e:
            if "حظر" in str(e):
                print("✅ حماية Rate Limiting تعمل بشكل صحيح")
            else:
                print(f"❌ خطأ غير متوقع: {e}")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في اختبار Rate Limiting: {e}")
        return False
    
    # اختبار 6: اختبار جامع الأخبار الآمن
    print("\n6️⃣ اختبار جامع الأخبار الآمن...")
    try:
        collector = iNewsCollector()
        
        if collector.get_access_token():
            print("✅ جامع الأخبار يعمل مع النظام الآمن")
        else:
            print("❌ فشل في تهيئة جامع الأخبار")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في جامع الأخبار: {e}")
        return False
    
    # اختبار 7: فحص الملفات الحساسة
    print("\n7️⃣ فحص الملفات الحساسة...")
    sensitive_files = ['.env', 'security.py', 'inews_collector.py']
    
    for file in sensitive_files:
        if os.path.exists(file):
            # فحص صلاحيات الملف (في Windows قد لا يعمل بشكل مثالي)
            try:
                stat = os.stat(file)
                print(f"✅ {file} موجود")
            except Exception as e:
                print(f"⚠️ تحذير: مشكلة في ملف {file}: {e}")
        else:
            print(f"❌ ملف مفقود: {file}")
            return False
    
    # اختبار 8: فحص متغيرات البيئة
    print("\n8️⃣ فحص متغيرات البيئة...")
    required_vars = ['FACEBOOK_APP_ID', 'FACEBOOK_APP_SECRET', 'SECURITY_KEY', 'ADMIN_PASSWORD']
    
    for var in required_vars:
        value = security_manager.env_vars.get(var)
        if value and value not in ['your_app_id_here', 'your_app_secret_here', 'your_secret_security_key_here', 'your_admin_password_here']:
            print(f"✅ {var}: محدد بشكل صحيح")
        else:
            print(f"❌ {var}: غير محدد أو يحتوي على قيمة افتراضية")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 جميع اختبارات الأمان نجحت!")
    print("🔒 النظام آمن ومحمي بشكل كامل")
    
    return True

def security_report():
    """تقرير أمني مفصل"""
    
    print("\n📊 تقرير الأمان المفصل")
    print("=" * 50)
    
    # معلومات النظام
    print(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💻 نظام التشغيل: {os.name}")
    print(f"📁 مجلد العمل: {os.getcwd()}")
    
    # فحص الملفات
    print(f"\n📁 الملفات الموجودة:")
    for file in os.listdir('.'):
        if os.path.isfile(file):
            size = os.path.getsize(file)
            print(f"  📄 {file} ({size} بايت)")
    
    # فحص المجلدات الآمنة
    secure_dir = "secure_data"
    if os.path.exists(secure_dir):
        files = os.listdir(secure_dir)
        print(f"\n🔒 الملفات الآمنة ({len(files)} ملف):")
        for file in files:
            print(f"  🔐 {file}")
    else:
        print(f"\n🔒 مجلد البيانات الآمنة: غير موجود (سيتم إنشاؤه عند الحاجة)")
    
    # فحص السجلات
    log_files = ['security.log', 'inews.log']
    print(f"\n📋 ملفات السجلات:")
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            print(f"  📝 {log_file} ({size} بايت)")
        else:
            print(f"  📝 {log_file}: غير موجود")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    print("🔒 نظام اختبار الأمان - iNews")
    print("تطوير: ياسر الجبوري")
    print("=" * 50)
    
    # تشغيل الاختبارات
    if test_security_system():
        security_report()
        print("\n✅ النظام جاهز للاستخدام الآمن!")
    else:
        print("\n❌ يرجى إصلاح مشاكل الأمان قبل الاستخدام!")
        print("📖 راجع ملف SECURITY_README.md للمساعدة")
