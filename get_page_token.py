#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الحصول على Page Access Token من Facebook
"""

import webbrowser
import requests
import os
from dotenv import load_dotenv

load_dotenv()

def open_page_token_explorer():
    """فتح Graph API Explorer للحصول على Page Access Token"""
    print("📄 الحصول على Page Access Token من Facebook")
    print("=" * 60)
    
    app_id = os.getenv('FACEBOOK_APP_ID')
    
    if not app_id:
        print("❌ لا يوجد App ID في ملف .env")
        return False
    
    print(f"📱 App ID: {app_id}")
    print(f"🎯 الهدف: الحصول على Page Access Token مع صلاحيات أكثر")
    print("=" * 60)
    
    # رابط Graph API Explorer
    url = f"https://developers.facebook.com/tools/explorer/{app_id}/?method=GET&path=me%2Faccounts&version=v18.0"
    
    try:
        print("🔗 فتح Graph API Explorer...")
        webbrowser.open(url)
        print("✅ تم فتح الرابط في المتصفح")
        
        print(f"\n📋 الخطوات للحصول على Page Token:")
        print("=" * 60)
        
        print("1️⃣ **تأكد من التطبيق:**")
        print(f"   • يجب أن يظهر App ID: {app_id}")
        
        print(f"\n2️⃣ **احصل على User Token أولاً:**")
        print(f"   • اضغط 'Get Token' > 'Get User Access Token'")
        print(f"   • أضف صلاحية: pages_show_list")
        print(f"   • أضف صلاحية: manage_pages (إذا ظهرت)")
        print(f"   • اضغط 'Generate Access Token'")
        
        print(f"\n3️⃣ **اجلب قائمة الصفحات:**")
        print(f"   • في المسار (Path)، اكتب: me/accounts")
        print(f"   • اضغط 'Submit'")
        print(f"   • ستظهر قائمة بصفحاتك")
        
        print(f"\n4️⃣ **احصل على Page Token:**")
        print(f"   • اضغط 'Get Token' > 'Get Page Access Token'")
        print(f"   • اختر صفحتك من القائمة")
        print(f"   • اختر الصلاحيات المطلوبة:")
        print(f"     ✓ pages_read_engagement")
        print(f"     ✓ pages_show_list")
        print(f"     ✓ read_page_mailboxes")
        print(f"   • اضغط 'Generate Access Token'")
        
        print(f"\n5️⃣ **انسخ Page Token:**")
        print(f"   • انسخ الـ Page Access Token")
        print(f"   • يبدأ عادة بـ EAAB...")
        print(f"   • هذا Token خاص بصفحتك")
        
        print(f"\n💡 **فوائد Page Token:**")
        print(f"   • صلاحيات أكثر من User Token")
        print(f"   • يمكن قراءة منشورات صفحات أخرى")
        print(f"   • أقل قيود من Facebook")
        print(f"   • مدة صلاحية أطول")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في فتح المتصفح: {e}")
        print(f"🔗 افتح هذا الرابط يدوياً:")
        print(f"{url}")
        return False

def save_page_token():
    """حفظ Page Access Token في ملف .env"""
    print(f"\n💾 حفظ Page Access Token")
    print("=" * 60)
    
    token = input("🔑 الصق الـ Page Access Token هنا: ").strip()
    
    if not token:
        print("❌ لم يتم إدخال token")
        return False
    
    try:
        # قراءة ملف .env
        env_file = ".env"
        lines = []
        
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        
        # إضافة Page Access Token
        page_token_line = f"FACEBOOK_PAGE_ACCESS_TOKEN={token}\n"
        
        # البحث عن السطر الموجود
        found = False
        for i, line in enumerate(lines):
            if line.startswith('FACEBOOK_PAGE_ACCESS_TOKEN='):
                lines[i] = page_token_line
                found = True
                break
        
        if not found:
            # إضافة السطر بعد User Token
            for i, line in enumerate(lines):
                if line.startswith('FACEBOOK_USER_ACCESS_TOKEN='):
                    lines.insert(i + 1, page_token_line)
                    found = True
                    break
            
            if not found:
                lines.append(page_token_line)
        
        # حفظ الملف
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم حفظ Page Token في ملف .env")
        
        # إعادة تحميل متغيرات البيئة
        load_dotenv(override=True)
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في حفظ Page Token: {e}")
        return False

def test_page_token():
    """اختبار Page Access Token"""
    print(f"\n🧪 اختبار Page Access Token")
    print("=" * 60)
    
    page_token = os.getenv('FACEBOOK_PAGE_ACCESS_TOKEN')
    
    if not page_token:
        print("❌ لا يوجد Page Access Token للاختبار")
        return False
    
    print(f"🔍 اختبار Page Token...")
    
    # اختبارات للصفحة
    tests = [
        {
            'name': 'معلومات الصفحة',
            'url': 'https://graph.facebook.com/v18.0/me',
            'params': {'fields': 'id,name,category,fan_count'}
        },
        {
            'name': 'منشورات الصفحة',
            'url': 'https://graph.facebook.com/v18.0/me/posts',
            'params': {'limit': 5, 'fields': 'id,message,created_time'}
        },
        {
            'name': 'صفحات أخرى (اختبار)',
            'url': 'https://graph.facebook.com/v18.0/facebook',
            'params': {'fields': 'id,name,category'}
        }
    ]
    
    successful_tests = 0
    
    for test in tests:
        print(f"\n🔍 اختبار: {test['name']}")
        
        try:
            params = {'access_token': page_token}
            params.update(test['params'])
            
            response = requests.get(test['url'], params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data:
                    items = data['data']
                    print(f"   ✅ نجح! عدد العناصر: {len(items)}")
                    if items and 'message' in items[0]:
                        message = items[0]['message'][:50] + "..." if len(items[0]['message']) > 50 else items[0]['message']
                        print(f"   📝 مثال: {message}")
                else:
                    print(f"   ✅ نجح! البيانات: {data.get('name', 'متوفر')}")
                
                successful_tests += 1
                
            else:
                error_data = response.json() if response.content else {}
                error_msg = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"   ❌ فشل: {error_msg}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
    
    print(f"\n📊 النتائج: {successful_tests}/{len(tests)} اختبار نجح")
    
    if successful_tests >= 1:
        print(f"🎉 Page Token يعمل!")
        return True
    else:
        print(f"❌ Page Token يحتاج تحسين")
        return False

def create_page_instructions():
    """تعليمات إنشاء صفحة Facebook"""
    print(f"\n📄 تعليمات إنشاء صفحة Facebook")
    print("=" * 60)
    
    print(f"🎯 إذا لم تكن لديك صفحة Facebook:")
    print(f"")
    print(f"1️⃣ **إنشاء الصفحة:**")
    print(f"   🔗 اذهب لـ: https://www.facebook.com/pages/create")
    print(f"   📝 اسم الصفحة: 'أخبار ياسر' أو أي اسم")
    print(f"   📂 نوع الصفحة: 'إعلام/أخبار' أو 'مدونة شخصية'")
    print(f"   📸 أضف صورة للصفحة (اختياري)")
    print(f"")
    print(f"2️⃣ **إعداد الصفحة:**")
    print(f"   📝 أضف وصف للصفحة: 'صفحة لجمع الأخبار'")
    print(f"   🌐 أضف موقع ويب (اختياري)")
    print(f"   📞 أضف معلومات الاتصال (اختياري)")
    print(f"")
    print(f"3️⃣ **نشر منشور تجريبي:**")
    print(f"   📝 اكتب منشور: 'مرحباً بكم في صفحة الأخبار'")
    print(f"   📤 انشر المنشور")
    print(f"")
    print(f"4️⃣ **الحصول على Page Token:**")
    print(f"   🔧 استخدم الأداة أعلاه للحصول على Page Token")
    
    try:
        webbrowser.open("https://www.facebook.com/pages/create")
        print(f"\n✅ تم فتح صفحة إنشاء الصفحات")
    except:
        print(f"\n🔗 افتح هذا الرابط لإنشاء صفحة:")
        print(f"https://www.facebook.com/pages/create")

def main():
    """الدالة الرئيسية"""
    print("📄 الحصول على Page Access Token - الحل الأمثل")
    print("=" * 60)
    print("🎯 Page Token له صلاحيات أكثر من User Token")
    print("=" * 60)
    
    # تعليمات إنشاء صفحة
    create_page_instructions()
    
    input(f"\n⏸️ اضغط Enter بعد إنشاء الصفحة...")
    
    # فتح Graph API Explorer للحصول على Page Token
    if open_page_token_explorer():
        input(f"\n⏸️ اضغط Enter بعد الحصول على Page Access Token...")
        
        # حفظ Page Token
        if save_page_token():
            # اختبار Page Token
            if test_page_token():
                print(f"\n🎉 ممتاز! Page Token يعمل")
                print(f"🚀 يمكنك الآن استخدام التطبيق مع صلاحيات أكثر")
                print(f"🌐 شغل التطبيق: python app.py")
            else:
                print(f"\n⚠️ Page Token يعمل جزئياً")
        else:
            print(f"\n❌ فشل في حفظ Page Token")
    else:
        print(f"\n❌ فشل في فتح Graph API Explorer")
    
    print(f"\n📋 الخطوات التالية:")
    print(f"1. تأكد من إنشاء صفحة Facebook")
    print(f"2. احصل على Page Access Token")
    print(f"3. شغل التطبيق: python app.py")
    print(f"4. جرب جمع الأخبار مع الصلاحيات الجديدة")

if __name__ == "__main__":
    main()
