#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جامع الأخبار من مصادر RSS
بديل لـ Facebook API عندما تكون الصلاحيات غير متاحة
"""

import feedparser
import requests
from datetime import datetime, timedelta
import json
import os
from security import security_manager
import time
import re
from urllib.parse import urlparse

class RSSNewsCollector:
    """جامع الأخبار من مصادر RSS"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # مصادر RSS للأخبار العربية والعالمية
        self.rss_sources = {
            'bbc_arabic': {
                'name': 'BBC العربية',
                'url': 'https://feeds.bbci.co.uk/arabic/rss.xml',
                'category': 'أخبار عالمية',
                'language': 'ar'
            },
            'aljazeera': {
                'name': 'الجزيرة نت',
                'url': 'https://www.aljazeera.net/xml/rss/all.xml',
                'category': 'أخبار عربية',
                'language': 'ar'
            },
            'alarabiya': {
                'name': 'العربية',
                'url': 'https://www.alarabiya.net/ar/rss.xml',
                'category': 'أخبار عربية',
                'language': 'ar'
            },
            'cnn_arabic': {
                'name': 'CNN بالعربية',
                'url': 'https://arabic.cnn.com/api/v1/rss/rss.xml',
                'category': 'أخبار عالمية',
                'language': 'ar'
            },
            'reuters_arabic': {
                'name': 'رويترز العربية',
                'url': 'https://ara.reuters.com/rssFeed/topNews',
                'category': 'أخبار عالمية',
                'language': 'ar'
            },
            'skynews_arabic': {
                'name': 'سكاي نيوز عربية',
                'url': 'https://www.skynewsarabia.com/rss.xml',
                'category': 'أخبار عربية',
                'language': 'ar'
            }
        }
    
    def collect_news_from_rss(self, source_ids=None, max_articles=10):
        """جمع الأخبار من مصادر RSS"""
        if source_ids is None:
            source_ids = list(self.rss_sources.keys())
        
        all_news = []
        successful_sources = 0
        
        print(f"🔄 بدء جمع الأخبار من {len(source_ids)} مصدر RSS...")
        
        for i, source_id in enumerate(source_ids, 1):
            if source_id not in self.rss_sources:
                print(f"❌ مصدر غير معروف: {source_id}")
                continue
            
            source = self.rss_sources[source_id]
            print(f"📰 [{i}/{len(source_ids)}] جلب الأخبار من: {source['name']}")
            
            try:
                news_items = self._fetch_rss_feed(source, max_articles)
                if news_items:
                    all_news.extend(news_items)
                    successful_sources += 1
                    print(f"✅ تم جلب {len(news_items)} خبر من {source['name']}")
                else:
                    print(f"⚠️ لم يتم جلب أخبار من {source['name']}")
                
                # توقف قصير بين الطلبات
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ خطأ في جلب الأخبار من {source['name']}: {str(e)}")
        
        print(f"\n📊 النتائج النهائية:")
        print(f"✅ تم جلب {len(all_news)} خبر من {successful_sources} مصدر")
        
        return all_news
    
    def _fetch_rss_feed(self, source, max_articles):
        """جلب الأخبار من مصدر RSS واحد"""
        try:
            # جلب RSS feed
            response = self.session.get(source['url'], timeout=10)
            response.raise_for_status()
            
            # تحليل RSS
            feed = feedparser.parse(response.content)
            
            if not feed.entries:
                return []
            
            news_items = []
            for entry in feed.entries[:max_articles]:
                news_item = self._parse_rss_entry(entry, source)
                if news_item:
                    news_items.append(news_item)
            
            return news_items
            
        except Exception as e:
            print(f"خطأ في جلب RSS من {source['name']}: {str(e)}")
            return []
    
    def _parse_rss_entry(self, entry, source):
        """تحليل عنصر RSS واحد"""
        try:
            # استخراج التاريخ
            published_time = None
            if hasattr(entry, 'published_parsed') and entry.published_parsed:
                published_time = datetime(*entry.published_parsed[:6])
            elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
                published_time = datetime(*entry.updated_parsed[:6])
            else:
                published_time = datetime.now()
            
            # تنظيف النص
            title = self._clean_text(entry.get('title', ''))
            summary = self._clean_text(entry.get('summary', ''))
            
            # إنشاء عنصر الخبر
            news_item = {
                'id': entry.get('id', entry.get('link', '')),
                'title': title,
                'summary': summary,
                'content': summary,  # RSS عادة يحتوي على ملخص فقط
                'link': entry.get('link', ''),
                'published_time': published_time.isoformat(),
                'source_name': source['name'],
                'source_id': source.get('id', ''),
                'category': source['category'],
                'language': source['language'],
                'collected_at': datetime.now().isoformat(),
                'type': 'rss'
            }
            
            return news_item
            
        except Exception as e:
            print(f"خطأ في تحليل عنصر RSS: {str(e)}")
            return None
    
    def _clean_text(self, text):
        """تنظيف النص من HTML tags والمحارف الخاصة"""
        if not text:
            return ""
        
        # إزالة HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # إزالة المحارف الخاصة
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&quot;', '"')
        
        # تنظيف المسافات
        text = ' '.join(text.split())
        
        return text.strip()
    
    def save_news_to_file(self, news_data, filename=None):
        """حفظ الأخبار في ملف مشفر"""
        if not news_data:
            return False
        
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"rss_news_{timestamp}.json"
            
            # إنشاء مجلد secure_data إذا لم يكن موجود
            os.makedirs('secure_data', exist_ok=True)
            
            # حفظ البيانات مشفرة
            file_path = os.path.join('secure_data', filename)
            encrypted_data = security_manager.encrypt_data(json.dumps(news_data, ensure_ascii=False, indent=2))
            
            with open(file_path, 'wb') as f:
                f.write(encrypted_data)
            
            print(f"✅ تم حفظ {len(news_data)} خبر في الملف: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حفظ الأخبار: {str(e)}")
            return False
    
    def get_available_sources(self):
        """الحصول على قائمة المصادر المتاحة"""
        return [
            {
                'id': source_id,
                'name': source_data['name'],
                'category': source_data['category'],
                'language': source_data['language'],
                'type': 'rss'
            }
            for source_id, source_data in self.rss_sources.items()
        ]

# دالة مساعدة للاختبار
def test_rss_collector():
    """اختبار جامع RSS"""
    collector = RSSNewsCollector()
    
    print("🧪 اختبار جامع الأخبار من RSS...")
    print("=" * 50)
    
    # جلب الأخبار من مصدرين فقط للاختبار
    test_sources = ['bbc_arabic', 'aljazeera']
    news = collector.collect_news_from_rss(test_sources, max_articles=5)
    
    if news:
        print(f"\n✅ تم جلب {len(news)} خبر بنجاح!")
        
        # حفظ الأخبار
        if collector.save_news_to_file(news):
            print("✅ تم حفظ الأخبار بنجاح!")
        
        # عرض أول خبر كمثال
        if news:
            print("\n📰 مثال على خبر:")
            print(f"العنوان: {news[0]['title'][:100]}...")
            print(f"المصدر: {news[0]['source_name']}")
            print(f"التاريخ: {news[0]['published_time']}")
    else:
        print("❌ فشل في جلب الأخبار")

if __name__ == "__main__":
    test_rss_collector()
