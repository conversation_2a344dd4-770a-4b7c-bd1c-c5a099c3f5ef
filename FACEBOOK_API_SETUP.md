# 🔧 دليل إعداد Facebook API لـ iNews

## ❌ المشكلة الحالية

```
"This endpoint requires the 'pages_read_engagement' permission or the 'Page Public Content Access' feature"
```

Facebook API يتطلب صلاحيات خاصة لم تعد متاحة بسهولة.

## 🛠️ الحلول المطلوبة

### **الحل الأول: تحديث إعدادات Facebook App**

#### **1. الذهاب إلى Facebook Developers**
```
https://developers.facebook.com/apps/
```

#### **2. اختيار التطبيق الخاص بك**
- اذهب إلى التطبيق المستخدم حالياً
- App ID: `1905779223574690`

#### **3. إضافة الصلاحيات المطلوبة**

##### **أ) في قسم "App Review":**
```
- اطلب "Page Public Content Access"
- اطلب "pages_read_engagement"
- قدم تبرير للاستخدام
```

##### **ب) في قسم "Permissions and Features":**
```
✅ Page Public Content Access
✅ pages_read_engagement  
✅ pages_show_list
✅ public_profile
```

#### **4. ملء نموذج المراجعة**
```
الغرض: جمع الأخبار من الصفحات العامة
الاستخدام: نظام إدارة أخبار شخصي
نوع البيانات: المنشورات العامة فقط
```

### **الحل الثاني: استخدام Graph API Explorer**

#### **1. الذهاب إلى Graph API Explorer**
```
https://developers.facebook.com/tools/explorer/
```

#### **2. اختبار الصلاحيات**
```
GET /PAGE_ID/posts
مع الصلاحيات:
- pages_read_engagement
- Page Public Content Access
```

#### **3. الحصول على Access Token جديد**
```
- اختر التطبيق الصحيح
- أضف الصلاحيات المطلوبة
- انسخ الـ Access Token الجديد
```

### **الحل الثالث: تحديث بيانات التطبيق**

#### **1. تحديث ملف .env**
```env
# بيانات Facebook API المحدثة
FACEBOOK_APP_ID=your_new_app_id
FACEBOOK_APP_SECRET=your_new_app_secret
FACEBOOK_ACCESS_TOKEN=your_new_access_token
```

#### **2. استخدام Page Access Token**
بدلاً من App Access Token، استخدم Page Access Token:
```
https://graph.facebook.com/PAGE_ID?fields=access_token&access_token=YOUR_USER_TOKEN
```

## 🔄 الحل المؤقت: تحديث الكود

### **تحديث inews_collector.py**

دعني أحدث الكود ليتعامل مع القيود الجديدة:

```python
def get_access_token(self):
    """الحصول على Access Token مع معالجة أفضل للأخطاء"""
    try:
        # جرب Page Access Token أولاً
        if hasattr(self, 'page_access_token'):
            return self.page_access_token
        
        # جرب User Access Token
        if hasattr(self, 'user_access_token'):
            return self.user_access_token
            
        # الطريقة التقليدية
        url = f"https://graph.facebook.com/oauth/access_token"
        params = {
            'client_id': self.app_id,
            'client_secret': self.app_secret,
            'grant_type': 'client_credentials'
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            return response.json().get('access_token')
        else:
            return None
            
    except Exception as e:
        print(f"خطأ في الحصول على Access Token: {e}")
        return None
```

## 📋 خطوات العمل الفورية

### **1. تحقق من حالة التطبيق**
```bash
# في المتصفح، اذهب إلى:
https://developers.facebook.com/apps/1905779223574690/dashboard/
```

### **2. تحقق من الصلاحيات**
```bash
# في Graph API Explorer:
GET /me/permissions
```

### **3. اختبر الوصول للصفحات**
```bash
# جرب صفحة عامة معروفة:
GET /facebook/posts?limit=5
```

## ⚠️ ملاحظات مهمة

### **قيود Facebook الجديدة:**
- **مراجعة التطبيق مطلوبة** للصلاحيات المتقدمة
- **الصفحات العامة فقط** يمكن الوصول إليها
- **حدود معدل الطلبات** أكثر صرامة

### **البدائل المؤقتة:**
- استخدام **صفحات تملكها** أنت شخصياً
- استخدام **Page Access Tokens** بدلاً من App Tokens
- التركيز على **الصفحات العامة الكبيرة** فقط

## 🎯 الخطوات التالية

### **الأولوية الأولى:**
1. **تحديث إعدادات Facebook App**
2. **طلب الصلاحيات المطلوبة**
3. **اختبار مع صفحات عامة معروفة**

### **الأولوية الثانية:**
1. **تحسين معالجة الأخطاء**
2. **إضافة رسائل خطأ واضحة**
3. **توفير بدائل للمستخدم**

---

## 🔗 روابط مفيدة

- [Facebook Developers Console](https://developers.facebook.com/apps/)
- [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
- [Facebook API Documentation](https://developers.facebook.com/docs/graph-api/)
- [App Review Process](https://developers.facebook.com/docs/app-review/)

**💡 نصيحة: ابدأ بطلب مراجعة التطبيق من Facebook للحصول على الصلاحيات المطلوبة.**
