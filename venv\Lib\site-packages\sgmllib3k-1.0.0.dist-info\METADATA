Metadata-Version: 2.4
Name: sgmllib3k
Version: 1.0.0
Summary: Py3k port of sgmllib.
Home-page: http://hg.hardcoded.net/sgmllib
Author: Hardcoded Software
Author-email: <EMAIL>
License: BSD License
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python :: 3
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: summary

==================================================
sgmllib3k -- Py3k port of the old stdlib module
==================================================

sgmllib was dropped in Python 3. For those depending on it, that's somewhat infortunate. This is a quick and dirty port of this old module. I just ran 2to3 on it and published it. I don't indend to maintain it, so it might be a good idea to eventually think about finding another module to use.
