#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع الاستيرادات المطلوبة لمشروع iNews
"""

def test_imports():
    """اختبار جميع الاستيرادات"""
    print("🧪 اختبار الاستيرادات...")
    print("=" * 50)
    
    try:
        # Flask والمكتبات الأساسية
        print("📦 اختبار Flask...")
        from flask import Flask, render_template, request, jsonify, send_file, session, redirect, url_for, flash
        print("✅ Flask - تم بنجاح")
        
        # مكتبات Python الأساسية
        print("📦 اختبار المكتبات الأساسية...")
        import json
        import os
        from datetime import datetime, timedelta
        import time
        import logging
        import re
        import secrets
        print("✅ المكتبات الأساسية - تم بنجاح")
        
        # مكتبات الأمان
        print("📦 اختبار مكتبات الأمان...")
        import hashlib
        import base64
        from cryptography.fernet import Fernet
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        print("✅ مكتبات الأمان - تم بنجاح")
        
        # مكتبات الشبكة
        print("📦 اختبار مكتبات الشبكة...")
        import requests
        import feedparser
        print("✅ مكتبات الشبكة - تم بنجاح")
        
        # مكتبات معالجة البيانات
        print("📦 اختبار مكتبات البيانات...")
        import pandas as pd
        print("✅ مكتبات البيانات - تم بنجاح")
        
        # اختبار الاستيراد من الملفات المحلية
        print("📦 اختبار الملفات المحلية...")
        try:
            from security import security_manager, SecurityError
            print("✅ security.py - تم بنجاح")
        except Exception as e:
            print(f"⚠️ security.py - تحذير: {e}")
        
        try:
            from inews_collector import iNewsCollector
            print("✅ inews_collector.py - تم بنجاح")
        except Exception as e:
            print(f"⚠️ inews_collector.py - تحذير: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 جميع الاستيرادات تعمل بشكل صحيح!")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_flask_app():
    """اختبار إنشاء تطبيق Flask"""
    print("\n🌐 اختبار إنشاء تطبيق Flask...")
    
    try:
        from flask import Flask
        app = Flask(__name__)
        app.secret_key = 'test_key'
        
        @app.route('/')
        def test_route():
            return "Test successful!"
        
        print("✅ تطبيق Flask تم إنشاؤه بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء تطبيق Flask: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار شامل لمشروع iNews")
    print("=" * 60)
    
    # اختبار الاستيرادات
    imports_ok = test_imports()
    
    # اختبار Flask
    flask_ok = test_flask_app()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار")
    print("=" * 60)
    print(f"📦 الاستيرادات: {'✅ نجح' if imports_ok else '❌ فشل'}")
    print(f"🌐 Flask: {'✅ نجح' if flask_ok else '❌ فشل'}")
    
    if imports_ok and flask_ok:
        print("\n🎉 جميع الاختبارات نجحت! المشروع جاهز للتشغيل.")
        print("\n🚀 لتشغيل التطبيق:")
        print("   python app.py")
        print("\n🌐 ثم افتح المتصفح على:")
        print("   http://localhost:5000")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
