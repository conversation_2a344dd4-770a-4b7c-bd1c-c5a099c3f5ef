# 🔒 دليل الأمان - iNews

## ⚠️ تحذيرات أمنية مهمة

### 🚨 معلومات حساسة
- **لا تشارك ملف `.env` مع أحد أبداً**
- **احتفظ بنسخة احتياطية آمنة من بياناتك**
- **غير كلمات المرور بانتظام**

### 🔐 الملفات المحمية
```
.env                 # بيانات Facebook API
secure_data/         # البيانات المشفرة
*.log               # سجلات النظام
```

## 🛡️ ميزات الأمان المطبقة

### 1. **تشفير البيانات**
- جميع البيانات الحساسة مشفرة باستخدام Fernet
- مفاتيح التشفير محمية بـ PBKDF2
- البيانات المحفوظة محمية بتشفير AES-256

### 2. **حماية كلمات المرور**
- تشفير كلمات المرور باستخدام PBKDF2-SHA256
- Salt عشوائي لكل كلمة مرور
- حماية من هجمات القاموس

### 3. **حماية من الهجمات**
- حد أقصى 3 محاولات دخول خاطئة
- حظر IP لمدة 5 دقائق بعد المحاولات الفاشلة
- تسجيل جميع محاولات الدخول

### 4. **حماية الملفات**
- صلاحيات محدودة للملفات (600)
- مجلد آمن منفصل للبيانات الحساسة
- تنظيف تلقائي للملفات القديمة

### 5. **سجلات الأمان**
- تسجيل جميع العمليات الحساسة
- مراقبة محاولات الوصول غير المصرح بها
- تتبع استخدام بيانات Facebook API

## 🔧 إعداد الأمان

### 1. **تحديث كلمات المرور**
```bash
# في ملف .env
SECURITY_KEY=your_very_strong_security_key_here
ADMIN_PASSWORD=your_very_strong_admin_password_here
```

### 2. **تفعيل التشفير**
```bash
ENCRYPTION_ENABLED=true
```

### 3. **إعدادات إضافية**
```bash
# حد محاولات الدخول
MAX_LOGIN_ATTEMPTS=3

# مدة الحظر (بالثواني)
LOCKOUT_TIME=300

# مدة انتهاء الجلسة (بالساعات)
SESSION_TIMEOUT=24
```

## 📋 قائمة التحقق الأمني

### ✅ قبل الاستخدام
- [ ] تحديث `SECURITY_KEY` في `.env`
- [ ] تحديث `ADMIN_PASSWORD` في `.env`
- [ ] التأكد من وجود ملف `.gitignore`
- [ ] فحص صلاحيات الملفات
- [ ] تفعيل نظام السجلات

### ✅ أثناء الاستخدام
- [ ] مراقبة ملف `security.log`
- [ ] فحص محاولات الدخول المشبوهة
- [ ] نسخ احتياطية دورية للبيانات
- [ ] تنظيف الملفات القديمة

### ✅ بعد الاستخدام
- [ ] إغلاق جميع الجلسات
- [ ] مراجعة سجلات الأمان
- [ ] حفظ البيانات المهمة بأمان

## 🚨 في حالة الطوارئ

### إذا تم اختراق النظام:
1. **أوقف التطبيق فوراً**
2. **غير جميع كلمات المرور**
3. **احذف جميع الجلسات النشطة**
4. **راجع سجلات الأمان**
5. **أعد إنشاء مفاتيح التشفير**

### إذا فقدت كلمة المرور:
1. **احذف ملف `.env`**
2. **أعد إنشاء التطبيق في Facebook**
3. **أعد تكوين النظام من البداية**

## 📞 الدعم الأمني

### معلومات الاتصال:
- **البريد الإلكتروني**: <EMAIL>
- **في حالة الطوارئ**: اتصل فوراً

### الموارد المفيدة:
- [Facebook Security](https://developers.facebook.com/docs/facebook-login/security/)
- [Python Cryptography](https://cryptography.io/)
- [OWASP Security Guide](https://owasp.org/)

---

**⚠️ تذكر: الأمان مسؤولية مشتركة. كن حذراً دائماً!**
