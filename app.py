#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق ويب لجمع الأخبار من تلغرام
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
import asyncio
import os
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv
import threading
import time
from telegram_news_collector import TelegramNewsCollector

# تحميل متغيرات البيئة
load_dotenv()

# إنشاء التطبيق
app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'default_secret_key')

# متغيرات عامة
news_collector = None
collection_status = {
    'is_running': False,
    'last_update': None,
    'total_news': 0,
    'channels_count': 0,
    'error_message': None
}

latest_news = []
channels_list = []

def check_admin_password(password):
    """التحقق من كلمة مرور الإدارة"""
    admin_password = os.getenv('ADMIN_PASSWORD', 'admin123')
    return password == admin_password

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html', 
                         collection_status=collection_status,
                         latest_news=latest_news[:10])

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        password = request.form.get('password')
        if check_admin_password(password):
            session['logged_in'] = True
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.pop('logged_in', None)
    flash('تم تسجيل الخروج', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))
    
    return render_template('dashboard.html',
                         collection_status=collection_status,
                         channels_list=channels_list,
                         latest_news=latest_news[:20])

@app.route('/api/start_collection', methods=['POST'])
def start_collection():
    """بدء جمع الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    if collection_status['is_running']:
        return jsonify({'error': 'جمع الأخبار يعمل بالفعل'}), 400
    
    try:
        # بدء جمع الأخبار في خيط منفصل
        thread = threading.Thread(target=run_news_collection)
        thread.daemon = True
        thread.start()
        
        return jsonify({'message': 'تم بدء جمع الأخبار'})
    
    except Exception as e:
        return jsonify({'error': f'فشل في بدء جمع الأخبار: {str(e)}'}), 500

@app.route('/api/stop_collection', methods=['POST'])
def stop_collection():
    """إيقاف جمع الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    collection_status['is_running'] = False
    return jsonify({'message': 'تم إيقاف جمع الأخبار'})

@app.route('/api/status')
def get_status():
    """الحصول على حالة جمع الأخبار"""
    return jsonify(collection_status)

@app.route('/api/news')
def get_news():
    """الحصول على الأخبار"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    start = (page - 1) * per_page
    end = start + per_page
    
    news_page = latest_news[start:end]
    
    return jsonify({
        'news': news_page,
        'total': len(latest_news),
        'page': page,
        'per_page': per_page,
        'has_next': end < len(latest_news)
    })

@app.route('/api/channels')
def get_channels():
    """الحصول على قائمة القنوات"""
    return jsonify(channels_list)

@app.route('/news')
def news_page():
    """صفحة الأخبار"""
    return render_template('news.html', latest_news=latest_news)

@app.route('/api/export/<format>')
def export_news(format):
    """تصدير الأخبار"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    if not latest_news:
        return jsonify({'error': 'لا توجد أخبار للتصدير'}), 400
    
    try:
        if format == 'csv':
            filename = export_to_csv()
        elif format == 'json':
            filename = export_to_json()
        else:
            return jsonify({'error': 'تنسيق غير مدعوم'}), 400
        
        return jsonify({'message': f'تم التصدير بنجاح', 'filename': filename})
    
    except Exception as e:
        return jsonify({'error': f'فشل في التصدير: {str(e)}'}), 500

def run_news_collection():
    """تشغيل جمع الأخبار"""
    global news_collector, collection_status, latest_news, channels_list
    
    try:
        collection_status['is_running'] = True
        collection_status['error_message'] = None
        
        # إنشاء حلقة أحداث جديدة للخيط
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # تشغيل جمع الأخبار
        loop.run_until_complete(collect_news_async())
        
    except Exception as e:
        collection_status['error_message'] = str(e)
        print(f"❌ خطأ في جمع الأخبار: {e}")
    
    finally:
        collection_status['is_running'] = False

async def collect_news_async():
    """جمع الأخبار بشكل غير متزامن"""
    global news_collector, collection_status, latest_news, channels_list
    
    try:
        # إنشاء جامع الأخبار
        news_collector = TelegramNewsCollector()
        
        # بدء الاتصال
        if await news_collector.start_client():
            print("✅ تم الاتصال بتلغرام")
            
            # جلب القنوات
            channels = await news_collector.get_subscribed_channels()
            channels_list = channels
            
            # جمع الأخبار
            hours_back = 24
            max_channels = 20
            
            news = await news_collector.collect_all_news(hours_back, max_channels)
            
            if news:
                # تحديث البيانات العامة
                latest_news = news
                collection_status['total_news'] = len(news)
                collection_status['channels_count'] = len(set(item['channel_name'] for item in news))
                collection_status['last_update'] = datetime.now().isoformat()
                
                print(f"✅ تم جمع {len(news)} خبر")
            
            else:
                print("❌ لم يتم جمع أي أخبار")
        
        else:
            raise Exception("فشل في الاتصال بتلغرام")
    
    except Exception as e:
        raise e
    
    finally:
        if news_collector:
            await news_collector.close()

def export_to_csv():
    """تصدير الأخبار إلى CSV"""
    if news_collector and latest_news:
        news_collector.collected_news = latest_news
        return news_collector.save_to_csv()
    return None

def export_to_json():
    """تصدير الأخبار إلى JSON"""
    if news_collector and latest_news:
        news_collector.collected_news = latest_news
        return news_collector.save_to_json()
    return None

def auto_collect_news():
    """جمع الأخبار تلقائياً"""
    while True:
        try:
            # التحقق من إعدادات التشغيل التلقائي
            auto_start = os.getenv('AUTO_START_COLLECTION', 'True').lower() == 'true'
            interval = int(os.getenv('NEWS_UPDATE_INTERVAL', 300))  # 5 دقائق افتراضياً
            
            if auto_start and not collection_status['is_running']:
                print("🔄 بدء جمع الأخبار التلقائي...")
                run_news_collection()
            
            time.sleep(interval)
        
        except Exception as e:
            print(f"❌ خطأ في الجمع التلقائي: {e}")
            time.sleep(60)  # انتظار دقيقة قبل المحاولة مرة أخرى

@app.route('/setup')
def setup():
    """صفحة الإعداد الأولي"""
    return render_template('setup.html')

@app.route('/api/test_connection', methods=['POST'])
def test_connection():
    """اختبار الاتصال بتلغرام"""
    if not session.get('logged_in'):
        return jsonify({'error': 'غير مصرح'}), 401
    
    try:
        # اختبار سريع للاتصال
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async def test():
            collector = TelegramNewsCollector()
            result = await collector.start_client()
            await collector.close()
            return result
        
        success = loop.run_until_complete(test())
        
        if success:
            return jsonify({'message': 'تم الاتصال بتلغرام بنجاح'})
        else:
            return jsonify({'error': 'فشل في الاتصال بتلغرام'}), 500
    
    except Exception as e:
        return jsonify({'error': f'خطأ في الاتصال: {str(e)}'}), 500

if __name__ == '__main__':
    print("🚀 بدء تشغيل تطبيق جمع الأخبار من تلغرام")
    print("=" * 60)
    print("🌐 الواجهة متاحة على: http://localhost:5020")
    print("🔑 كلمة مرور الإدارة موجودة في ملف .env")
    print("=" * 60)
    
    # بدء الجمع التلقائي في خيط منفصل
    auto_thread = threading.Thread(target=auto_collect_news)
    auto_thread.daemon = True
    auto_thread.start()
    
    # تشغيل التطبيق
    port = int(os.getenv('PORT', 5020))
    app.run(host='0.0.0.0', port=port, debug=True)
