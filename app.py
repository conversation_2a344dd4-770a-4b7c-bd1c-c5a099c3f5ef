"""
iNews Web Interface
واجهة ويب آمنة لإدارة وعرض الأخبار من Facebook
🔒 محمي بنظام أمان متقدم
"""

from flask import Flask, render_template, request, jsonify, send_file, session, redirect, url_for, flash
import json
import os
from datetime import datetime, timedelta
import re
from inews_collector import iNewsCollector
from security import security_manager, SecurityError
import secrets

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)
app.permanent_session_lifetime = timedelta(hours=24)

class PageManager:
    """إدارة صفحات الأخبار"""
    
    def __init__(self):
        self.pages_file = "pages.json"
        self.load_pages()
    
    def load_pages(self):
        """تحميل قائمة الصفحات"""
        if os.path.exists(self.pages_file):
            with open(self.pages_file, 'r', encoding='utf-8') as f:
                self.pages = json.load(f)
        else:
            self.pages = []
    
    def save_pages(self):
        """حفظ قائمة الصفحات"""
        with open(self.pages_file, 'w', encoding='utf-8') as f:
            json.dump(self.pages, f, ensure_ascii=False, indent=2)
    
    def extract_page_id(self, url):
        """استخراج Page ID من رابط Facebook"""
        # أنماط مختلفة لروابط Facebook
        patterns = [
            r'facebook\.com/([^/?]+)',
            r'facebook\.com/pages/[^/]+/(\d+)',
            r'facebook\.com/profile\.php\?id=(\d+)',
            r'^(\d+)$'  # إذا كان Page ID مباشرة
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def add_page(self, url, name=None, category=None):
        """إضافة صفحة جديدة"""
        page_id = self.extract_page_id(url)
        if not page_id:
            return False, "رابط غير صحيح"
        
        # التحقق من عدم وجود الصفحة مسبقاً
        for page in self.pages:
            if page['id'] == page_id:
                return False, "الصفحة موجودة مسبقاً"
        
        page_data = {
            'id': page_id,
            'url': url,
            'name': name or f"صفحة {page_id}",
            'category': category or "عام",
            'added_at': datetime.now().isoformat(),
            'active': True
        }
        
        self.pages.append(page_data)
        self.save_pages()
        return True, "تم إضافة الصفحة بنجاح"
    
    def remove_page(self, page_id):
        """حذف صفحة"""
        self.pages = [p for p in self.pages if p['id'] != page_id]
        self.save_pages()
        return True, "تم حذف الصفحة"
    
    def toggle_page(self, page_id):
        """تفعيل/إلغاء تفعيل صفحة"""
        for page in self.pages:
            if page['id'] == page_id:
                page['active'] = not page['active']
                self.save_pages()
                return True, f"تم {'تفعيل' if page['active'] else 'إلغاء تفعيل'} الصفحة"
        return False, "الصفحة غير موجودة"
    
    def get_active_pages(self):
        """الحصول على الصفحات النشطة"""
        return [p for p in self.pages if p['active']]

# إنشاء مدير الصفحات
page_manager = PageManager()

# دوال الأمان
def login_required(f):
    """ديكوريتر للتحقق من تسجيل الدخول"""
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            # إذا كان الطلب AJAX، أرجع JSON بدلاً من إعادة التوجيه
            if request.is_json or request.headers.get('Content-Type') == 'application/json':
                return jsonify({
                    'success': False,
                    'message': 'مطلوب تسجيل الدخول للوصول لهذه الوظيفة',
                    'error_type': 'authentication_required'
                }), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        password = request.form.get('password')
        ip_address = request.remote_addr or 'unknown'

        try:
            if security_manager.authenticate_admin(password, ip_address):
                session['logged_in'] = True
                session['login_time'] = datetime.now().isoformat()
                session.permanent = True
                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('dashboard'))
        except SecurityError as e:
            flash(str(e), 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/')
def index():
    """الصفحة الرئيسية - عرض الأخبار"""
    return render_template('news.html')

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم - إدارة المصادر"""
    return render_template('dashboard.html', pages=page_manager.pages)

@app.route('/get_news')
def get_news():
    """جلب الأخبار من جميع المصادر النشطة"""
    try:
        # البحث عن ملفات الأخبار المحفوظة
        news_files = []
        if os.path.exists('secure_data'):
            for file in os.listdir('secure_data'):
                if file.startswith('facebook_news_') and file.endswith('.json'):
                    news_files.append(file)

        # ترتيب الملفات حسب التاريخ (الأحدث أولاً)
        news_files.sort(reverse=True)

        all_news = []
        # جلب الأخبار من أحدث 3 ملفات
        for file in news_files[:3]:
            try:
                news_data = security_manager.secure_file_load(file, decrypt=True)
                if isinstance(news_data, list):
                    all_news.extend(news_data)
                elif isinstance(news_data, str):
                    # إذا كان النص JSON
                    import json
                    news_data = json.loads(news_data)
                    all_news.extend(news_data)
            except:
                continue

        # ترتيب الأخبار حسب التاريخ
        all_news.sort(key=lambda x: x.get('created_time', ''), reverse=True)

        return jsonify({
            'success': True,
            'news': all_news[:50],  # أحدث 50 خبر
            'total': len(all_news)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})

@app.route('/add_page', methods=['POST'])
@login_required
def add_page():
    """إضافة صفحة جديدة"""
    data = request.get_json()
    url = data.get('url', '').strip()
    name = data.get('name', '').strip()
    category = data.get('category', '').strip()

    if not url:
        return jsonify({'success': False, 'message': 'الرابط مطلوب'})

    success, message = page_manager.add_page(url, name, category)
    return jsonify({'success': success, 'message': message})

@app.route('/remove_page', methods=['POST'])
@login_required
def remove_page():
    """حذف صفحة"""
    data = request.get_json()
    page_id = data.get('page_id')

    success, message = page_manager.remove_page(page_id)
    return jsonify({'success': success, 'message': message})

@app.route('/toggle_page', methods=['POST'])
@login_required
def toggle_page():
    """تفعيل/إلغاء تفعيل صفحة"""
    data = request.get_json()
    page_id = data.get('page_id')

    success, message = page_manager.toggle_page(page_id)
    return jsonify({'success': success, 'message': message})

@app.route('/collect_news', methods=['POST'])
@login_required
def collect_news():
    """جمع الأخبار من مصادر الفيسبوك فقط"""
    try:
        data = request.get_json()
        posts_per_page = data.get('posts_per_page', 5)

        # الحصول على الصفحات النشطة
        active_pages = page_manager.get_active_pages()

        if not active_pages:
            return jsonify({
                'success': False,
                'message': 'لا توجد مصادر فيسبوك نشطة. يرجى إضافة مصادر أولاً.'
            })

        # إنشاء جامع الأخبار من Facebook
        collector = iNewsCollector()

        # جمع الأخبار من مصادر الفيسبوك
        page_ids = [page['id'] for page in active_pages]
        news_data = collector.collect_news_from_pages(page_ids, posts_per_page)

        if news_data:
            # حفظ البيانات
            collector.save_to_json(news_data, secure=True)

            return jsonify({
                'success': True,
                'message': f'تم جمع {len(news_data)} خبر من {len(active_pages)} مصدر فيسبوك',
                'news_count': len(news_data),
                'sources_count': len(active_pages),
                'source': 'facebook'
            })
        else:
            # إذا فشل جمع الأخبار من Facebook
            error_msg = """
            🔴 مشكلة في Facebook API - الصلاحيات مطلوبة

            📊 التشخيص:
            • Facebook غير سياساته ويتطلب صلاحيات خاصة
            • التطبيق يحتاج "Page Public Content Access"
            • المشكلة تؤثر على جميع التطبيقات الجديدة

            🛠️ الحلول المتاحة:
            1. احصل على User Access Token من Graph API Explorer
            2. أنشئ صفحة فيسبوك خاصة بك واستخدم Page Token
            3. قدم طلب مراجعة لـ Facebook (يستغرق وقت)

            📋 للحل السريع:
            • راجع ملف FACEBOOK_SOLUTION.md
            • شغل facebook_api_tester.py للتشخيص
            • اتبع الخطوات في FACEBOOK_API_SETUP.md

            💡 نصيحة: استخدم صفحة فيسبوك تملكها للحصول على أفضل النتائج
            """

            return jsonify({
                'success': False,
                'message': 'مشكلة في صلاحيات Facebook API',
                'details': error_msg.strip(),
                'active_sources': len(active_pages),
                'error_type': 'facebook_permissions',
                'solution_files': ['FACEBOOK_SOLUTION.md', 'FACEBOOK_API_SETUP.md'],
                'test_command': 'python facebook_api_tester.py'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جمع الأخبار: {str(e)}'
        })

@app.route('/clear_news', methods=['POST'])
@login_required
def clear_news():
    """تنظيف جميع الأخبار المحفوظة"""
    try:
        # حذف جميع ملفات الأخبار من مجلد secure_data
        secure_data_dir = 'secure_data'
        deleted_files = 0

        if os.path.exists(secure_data_dir):
            for filename in os.listdir(secure_data_dir):
                # حذف ملفات الأخبار (Facebook و RSS)
                if (filename.startswith('facebook_news_') or filename.startswith('rss_news_')) and filename.endswith('.json'):
                    file_path = os.path.join(secure_data_dir, filename)
                    try:
                        os.remove(file_path)
                        deleted_files += 1
                        print(f"✅ تم حذف ملف الأخبار: {filename}")
                    except Exception as e:
                        print(f"❌ فشل في حذف ملف الأخبار {filename}: {str(e)}")

        return jsonify({
            'success': True,
            'message': f'تم تنظيف {deleted_files} ملف من الأخبار بنجاح'
        })

    except Exception as e:
        print(f"❌ خطأ في تنظيف الأخبار: {str(e)}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تنظيف الأخبار: {str(e)}'
        })

@app.route('/download/<filename>')
def download_file(filename):
    """تحميل ملف"""
    try:
        return send_file(filename, as_attachment=True)
    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل الملف: {str(e)}'})

@app.route('/test_connection')
def test_connection():
    """اختبار الاتصال مع Facebook API"""
    try:
        collector = iNewsCollector()
        success = collector.get_access_token()
        
        if success:
            return jsonify({'success': True, 'message': 'الاتصال ناجح'})
        else:
            return jsonify({'success': False, 'message': 'فشل الاتصال'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ: {str(e)}'})

if __name__ == '__main__':
    print("🚀 بدء تشغيل iNews Web Interface")
    print("📱 الواجهة متاحة على: http://localhost:5020")
    app.run(debug=True, host='0.0.0.0', port=5020)
