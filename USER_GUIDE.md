# 📱 دليل استخدام iNews

## 🚀 كيفية تشغيل النظام

### 1. تشغيل الخادم
```bash
python app.py
```

### 2. فتح المتصفح
اذهب إلى: http://localhost:5020

## 🔑 معلومات الدخول

- **كلمة مرور المدير:** `YaserAdmin2024!SecureNews@Protection`

## 📄 الصفحات المتاحة

### 🏠 الصفحة الرئيسية - عرض الأخبار
**الرابط:** http://localhost:5020

**الميزات:**
- ✅ عرض أحدث الأخبار
- ✅ إحصائيات مباشرة (إجمالي الأخبار، أخبار اليوم، المصادر النشطة)
- ✅ بحث في الأخبار
- ✅ فلترة حسب المصدر
- ✅ تحديث مباشر

**كيفية الاستخدام:**
1. افتح الصفحة الرئيسية
2. تصفح الأخبار المعروضة
3. استخدم مربع البحث للبحث في الأخبار
4. اختر مصدر معين من القائمة المنسدلة
5. اضغط "تحديث" لتحديث الأخبار

### ⚙️ لوحة التحكم - إدارة المصادر
**الرابط:** http://localhost:5020/dashboard

**الميزات:**
- ✅ إضافة مصادر جديدة من الفيسبوك
- ✅ إدارة المصادر (تفعيل/إلغاء تفعيل/حذف)
- ✅ جمع الأخبار من المصادر النشطة
- ✅ اختبار الاتصال مع Facebook API

**كيفية الاستخدام:**

#### إضافة مصدر جديد:
1. اذهب إلى لوحة التحكم
2. سجل دخولك بكلمة المرور
3. في قسم "إضافة مصدر جديد":
   - أدخل رابط صفحة الفيسبوك
   - أدخل اسم المصدر (اختياري)
   - اختر التصنيف
4. اضغط "إضافة المصدر"

#### جمع الأخبار:
1. اختر عدد المنشورات لكل صفحة
2. اضغط "جمع الأخبار الآن"
3. انتظر حتى اكتمال العملية

#### إدارة المصادر:
- **تفعيل/إلغاء تفعيل:** اضغط زر التشغيل/الإيقاف
- **حذف مصدر:** اضغط زر الحذف (سيطلب تأكيد)

## 🔧 حل المشاكل

### مشكلة: "لم يتم جمع أي أخبار"

**السبب:** قيود Facebook API تتطلب صلاحيات إضافية

**الحل المطبق:**
- ✅ النظام يستخدم تلقائياً مصادر RSS بديلة
- ✅ يتم جمع أخبار حقيقية من BBC، CNN، سكاي نيوز
- ✅ يتم إنشاء أخبار تجريبية إضافية

### مشكلة: "خطأ في تسجيل الدخول"

**الحلول:**
1. تأكد من كلمة المرور: `YaserAdmin2024!SecureNews@Protection`
2. انتظر 5 دقائق إذا تم حظر IP مؤقتاً
3. أعد تشغيل الخادم

### مشكلة: "لا تظهر الأخبار"

**الحلول:**
1. اذهب إلى لوحة التحكم
2. اضغط "جمع الأخبار الآن"
3. انتظر حتى اكتمال العملية
4. ارجع للصفحة الرئيسية واضغط "تحديث"

## 📊 أمثلة على روابط صفحات الفيسبوك

```
https://facebook.com/BBCArabic
https://facebook.com/aljazeera
https://facebook.com/alarabiya
https://facebook.com/skynewsarabia
https://facebook.com/cnnarabic
```

## 🔒 الأمان

### ميزات الأمان المطبقة:
- ✅ تشفير جميع البيانات الحساسة
- ✅ حماية من الهجمات المتكررة
- ✅ جلسات محدودة الوقت
- ✅ تسجيل جميع العمليات
- ✅ كلمات مرور قوية

### نصائح أمنية:
1. **لا تشارك كلمة المرور مع أحد**
2. **أغلق المتصفح بعد الانتهاء**
3. **راجع ملف `security.log` دورياً**
4. **احتفظ بنسخة احتياطية من ملف `.env`**

## 📁 الملفات المهمة

```
📁 iNews/
├── 📄 app.py                    # التطبيق الرئيسي
├── 📄 inews_collector.py        # جامع أخبار Facebook
├── 📄 alternative_news_collector.py  # جامع أخبار بديل
├── 📄 security.py               # نظام الأمان
├── 📄 .env                      # الإعدادات السرية
├── 📄 pages.json                # قائمة المصادر
├── 📁 secure_data/              # البيانات المشفرة
├── 📁 templates/                # قوالب HTML
└── 📄 *.log                     # ملفات السجلات
```

## 🆘 الدعم

### في حالة وجود مشاكل:
1. **راجع ملف `security.log`**
2. **راجع ملف `inews.log`**
3. **أعد تشغيل التطبيق**
4. **تواصل مع المطور**

### معلومات الاتصال:
- **البريد الإلكتروني:** <EMAIL>

---

**🎉 استمتع باستخدام iNews - نظامك الآمن لجمع الأخبار!**
