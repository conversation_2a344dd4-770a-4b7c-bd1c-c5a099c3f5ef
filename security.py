"""
🔒 نظام الأمان المتقدم لـ iNews
حماية شاملة للبيانات والحساب الشخصي
"""

import os
import hashlib
import secrets
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import json
import time
from datetime import datetime, timedelta
import logging

# إعداد نظام السجلات الأمني
logging.basicConfig(
    filename='security.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SecurityManager:
    """مدير الأمان الشامل"""
    
    def __init__(self):
        self.load_env()
        self.setup_encryption()
        self.failed_attempts = {}
        self.max_attempts = 3
        self.lockout_time = 300  # 5 دقائق
        
    def load_env(self):
        """تحميل متغيرات البيئة بأمان"""
        self.env_vars = {}
        try:
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        self.env_vars[key] = value
        except Exception as e:
            logging.error(f"فشل في تحميل ملف .env: {e}")
            raise SecurityError("فشل في تحميل إعدادات الأمان")
    
    def setup_encryption(self):
        """إعداد التشفير"""
        try:
            # إنشاء مفتاح تشفير من كلمة مرور
            password = self.env_vars.get('SECURITY_KEY', 'default_key').encode()
            salt = b'salt_for_inews_app'  # في الإنتاج، استخدم salt عشوائي
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            self.cipher = Fernet(key)
            
        except Exception as e:
            logging.error(f"فشل في إعداد التشفير: {e}")
            raise SecurityError("فشل في إعداد نظام التشفير")
    
    def encrypt_data(self, data):
        """تشفير البيانات"""
        try:
            if isinstance(data, str):
                data = data.encode()
            elif isinstance(data, dict):
                data = json.dumps(data).encode()
            
            encrypted = self.cipher.encrypt(data)
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logging.error(f"فشل في تشفير البيانات: {e}")
            return None
    
    def decrypt_data(self, encrypted_data):
        """فك تشفير البيانات"""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logging.error(f"فشل في فك التشفير: {e}")
            return None
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        salt = secrets.token_hex(16)
        pwd_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{pwd_hash.hex()}"
    
    def verify_password(self, password, hashed):
        """التحقق من كلمة المرور"""
        try:
            salt, pwd_hash = hashed.split(':')
            return pwd_hash == hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
        except:
            return False
    
    def check_rate_limit(self, identifier):
        """فحص محاولات الدخول المتكررة"""
        now = time.time()
        
        if identifier in self.failed_attempts:
            attempts, last_attempt = self.failed_attempts[identifier]
            
            # إذا انتهت فترة الحظر
            if now - last_attempt > self.lockout_time:
                del self.failed_attempts[identifier]
                return True
            
            # إذا تجاوز الحد الأقصى
            if attempts >= self.max_attempts:
                logging.warning(f"محاولة دخول محظورة من: {identifier}")
                return False
        
        return True
    
    def record_failed_attempt(self, identifier):
        """تسجيل محاولة دخول فاشلة"""
        now = time.time()
        
        if identifier in self.failed_attempts:
            attempts, _ = self.failed_attempts[identifier]
            self.failed_attempts[identifier] = (attempts + 1, now)
        else:
            self.failed_attempts[identifier] = (1, now)
        
        logging.warning(f"محاولة دخول فاشلة من: {identifier}")
    
    def authenticate_admin(self, password, ip_address="unknown"):
        """مصادقة المدير"""
        # فحص محاولات الدخول
        if not self.check_rate_limit(ip_address):
            raise SecurityError("تم حظر IP مؤقتاً بسبب محاولات دخول متكررة")
        
        # التحقق من كلمة المرور
        admin_password = self.env_vars.get('ADMIN_PASSWORD', 'admin123')
        
        if password == admin_password:
            logging.info(f"دخول ناجح من: {ip_address}")
            # مسح محاولات فاشلة سابقة
            if ip_address in self.failed_attempts:
                del self.failed_attempts[ip_address]
            return True
        else:
            self.record_failed_attempt(ip_address)
            raise SecurityError("كلمة مرور خاطئة")
    
    def get_facebook_credentials(self):
        """الحصول على بيانات Facebook بأمان"""
        app_id = self.env_vars.get('FACEBOOK_APP_ID')
        app_secret = self.env_vars.get('FACEBOOK_APP_SECRET')
        
        if not app_id or not app_secret:
            raise SecurityError("بيانات Facebook غير مكتملة")
        
        # تسجيل الوصول للبيانات الحساسة
        logging.info("تم الوصول لبيانات Facebook API")
        
        return app_id, app_secret
    
    def secure_file_save(self, data, filename, encrypt=True):
        """حفظ ملف بأمان"""
        try:
            # إنشاء مجلد آمن إذا لم يكن موجود
            secure_dir = "secure_data"
            if not os.path.exists(secure_dir):
                os.makedirs(secure_dir, mode=0o700)  # صلاحيات محدودة
            
            filepath = os.path.join(secure_dir, filename)
            
            if encrypt:
                # تشفير البيانات قبل الحفظ
                if isinstance(data, (dict, list)):
                    data = json.dumps(data, ensure_ascii=False)
                
                encrypted_data = self.encrypt_data(data)
                if not encrypted_data:
                    raise SecurityError("فشل في تشفير البيانات")
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(encrypted_data)
            else:
                # حفظ بدون تشفير (للملفات العامة)
                if isinstance(data, (dict, list)):
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                else:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(data)
            
            # تعيين صلاحيات محدودة للملف
            os.chmod(filepath, 0o600)
            
            logging.info(f"تم حفظ ملف آمن: {filename}")
            return filepath
            
        except Exception as e:
            logging.error(f"فشل في حفظ الملف الآمن: {e}")
            raise SecurityError(f"فشل في حفظ الملف: {str(e)}")
    
    def secure_file_load(self, filename, decrypt=True):
        """تحميل ملف آمن"""
        try:
            filepath = os.path.join("secure_data", filename)
            
            if not os.path.exists(filepath):
                raise SecurityError("الملف غير موجود")
            
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if decrypt:
                decrypted = self.decrypt_data(content)
                if not decrypted:
                    raise SecurityError("فشل في فك تشفير الملف")
                
                try:
                    return json.loads(decrypted)
                except:
                    return decrypted
            else:
                try:
                    return json.loads(content)
                except:
                    return content
                    
        except Exception as e:
            logging.error(f"فشل في تحميل الملف الآمن: {e}")
            raise SecurityError(f"فشل في تحميل الملف: {str(e)}")
    
    def generate_session_token(self):
        """إنشاء رمز جلسة آمن"""
        token = secrets.token_urlsafe(32)
        timestamp = datetime.now().isoformat()
        
        session_data = {
            'token': token,
            'created_at': timestamp,
            'expires_at': (datetime.now() + timedelta(hours=24)).isoformat()
        }
        
        logging.info("تم إنشاء رمز جلسة جديد")
        return session_data
    
    def cleanup_old_files(self, days=7):
        """تنظيف الملفات القديمة"""
        try:
            secure_dir = "secure_data"
            if not os.path.exists(secure_dir):
                return
            
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            cleaned_count = 0
            
            for filename in os.listdir(secure_dir):
                filepath = os.path.join(secure_dir, filename)
                if os.path.getmtime(filepath) < cutoff_time:
                    os.remove(filepath)
                    cleaned_count += 1
            
            logging.info(f"تم تنظيف {cleaned_count} ملف قديم")
            
        except Exception as e:
            logging.error(f"فشل في تنظيف الملفات: {e}")

class SecurityError(Exception):
    """استثناء أمني مخصص"""
    pass

# إنشاء مثيل مدير الأمان
security_manager = SecurityManager()

# دوال مساعدة للاستخدام السريع
def encrypt(data):
    return security_manager.encrypt_data(data)

def decrypt(data):
    return security_manager.decrypt_data(data)

def authenticate(password, ip="unknown"):
    return security_manager.authenticate_admin(password, ip)

def get_facebook_creds():
    return security_manager.get_facebook_credentials()

def secure_save(data, filename, encrypt=True):
    return security_manager.secure_file_save(data, filename, encrypt)

def secure_load(filename, decrypt=True):
    return security_manager.secure_file_load(filename, decrypt)
