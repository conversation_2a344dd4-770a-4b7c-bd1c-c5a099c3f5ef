#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملفات أخبار تجريبية لاختبار دالة التنظيف
"""

import os
import json
from datetime import datetime
from security import security_manager

def create_test_news_files():
    """إنشاء ملفات أخبار تجريبية"""
    print("📰 إنشاء ملفات أخبار تجريبية...")
    
    # إنشاء مجلد secure_data إذا لم يكن موجود
    os.makedirs('secure_data', exist_ok=True)
    
    # بيانات أخبار تجريبية
    test_news_data = [
        {
            'page_name': 'صفحة أخبار تجريبية',
            'page_id': 'test_page_1',
            'post_id': 'test_post_1',
            'message': 'هذا خبر تجريبي للاختبار',
            'story': 'قصة تجريبية',
            'created_time': datetime.now().isoformat(),
            'type': 'status',
            'link': 'https://example.com',
            'reactions_count': 10,
            'comments_count': 5,
            'shares_count': 2,
            'collected_at': datetime.now().isoformat()
        },
        {
            'page_name': 'صفحة أخبار تجريبية 2',
            'page_id': 'test_page_2',
            'post_id': 'test_post_2',
            'message': 'خبر تجريبي آخر للاختبار',
            'story': 'قصة تجريبية أخرى',
            'created_time': datetime.now().isoformat(),
            'type': 'link',
            'link': 'https://example2.com',
            'reactions_count': 20,
            'comments_count': 8,
            'shares_count': 4,
            'collected_at': datetime.now().isoformat()
        }
    ]
    
    # إنشاء ملفات Facebook news
    for i in range(3):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"facebook_news_{timestamp}_{i}.json"
        
        try:
            # حفظ البيانات مشفرة باستخدام secure_file_save
            file_path = security_manager.secure_file_save(test_news_data, filename, encrypt=True)
            print(f"✅ تم إنشاء ملف: {filename}")

        except Exception as e:
            print(f"❌ فشل في إنشاء ملف {filename}: {e}")
    
    # إنشاء ملفات RSS news
    rss_news_data = [
        {
            'id': 'rss_test_1',
            'title': 'خبر RSS تجريبي',
            'summary': 'ملخص خبر RSS تجريبي',
            'content': 'محتوى خبر RSS تجريبي',
            'link': 'https://rss-example.com/news1',
            'published_time': datetime.now().isoformat(),
            'source_name': 'مصدر RSS تجريبي',
            'source_id': 'rss_test_source',
            'category': 'أخبار تجريبية',
            'language': 'ar',
            'collected_at': datetime.now().isoformat(),
            'type': 'rss'
        }
    ]
    
    for i in range(2):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"rss_news_{timestamp}_{i}.json"
        
        try:
            # حفظ البيانات مشفرة باستخدام secure_file_save
            file_path = security_manager.secure_file_save(rss_news_data, filename, encrypt=True)
            print(f"✅ تم إنشاء ملف RSS: {filename}")

        except Exception as e:
            print(f"❌ فشل في إنشاء ملف RSS {filename}: {e}")

def count_news_files():
    """عد ملفات الأخبار الموجودة"""
    if not os.path.exists('secure_data'):
        return 0
    
    count = 0
    for filename in os.listdir('secure_data'):
        if (filename.startswith('facebook_news_') or filename.startswith('rss_news_')) and filename.endswith('.json'):
            count += 1
    
    return count

def main():
    """الدالة الرئيسية"""
    print("🧪 إنشاء ملفات أخبار تجريبية لاختبار دالة التنظيف")
    print("=" * 60)
    
    # عد الملفات قبل الإنشاء
    files_before = count_news_files()
    print(f"📁 عدد ملفات الأخبار قبل الإنشاء: {files_before}")
    
    # إنشاء ملفات تجريبية
    create_test_news_files()
    
    # عد الملفات بعد الإنشاء
    files_after = count_news_files()
    print(f"📁 عدد ملفات الأخبار بعد الإنشاء: {files_after}")
    
    print(f"\n✅ تم إنشاء {files_after - files_before} ملف أخبار تجريبي")
    print("🧪 يمكنك الآن اختبار دالة تنظيف الأخبار")

if __name__ == "__main__":
    main()
