{% extends "base.html" %}

{% block title %}الرئيسية - جامع الأخبار من تلغرام{% endblock %}

{% block content %}
<div class="row">
    <!-- معلومات التطبيق -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-body text-center">
                <h1 class="card-title display-4 mb-4">
                    <i class="bi bi-telegram text-primary"></i>
                    جامع الأخبار من تلغرام
                </h1>
                <p class="lead mb-4">
                    اجمع الأخبار تلقائياً من قنوات تلغرام التي اشتركت فيها
                </p>
                
                <div class="row text-center mb-4">
                    <div class="col-md-4">
                        <div class="p-3">
                            <i class="bi bi-newspaper display-6 text-primary"></i>
                            <h5 class="mt-2">جمع تلقائي</h5>
                            <p class="text-muted">جمع الأخبار من قنواتك تلقائياً</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="p-3">
                            <i class="bi bi-funnel display-6 text-success"></i>
                            <h5 class="mt-2">فلترة ذكية</h5>
                            <p class="text-muted">فلترة المحتوى الإخباري تلقائياً</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="p-3">
                            <i class="bi bi-download display-6 text-info"></i>
                            <h5 class="mt-2">تصدير سهل</h5>
                            <p class="text-muted">تصدير النتائج بصيغ مختلفة</p>
                        </div>
                    </div>
                </div>
                
                {% if not session.logged_in %}
                <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg">
                    <i class="bi bi-box-arrow-in-right"></i>
                    تسجيل الدخول للبدء
                </a>
                {% else %}
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg">
                    <i class="bi bi-speedometer2"></i>
                    اذهب للوحة التحكم
                </a>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- حالة النظام -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    حالة النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>حالة الجمع:</strong>
                    <div id="collection-status">
                        {% if collection_status.is_running %}
                            <span class="status-badge bg-success">جاري الجمع...</span>
                        {% elif collection_status.error_message %}
                            <span class="status-badge bg-danger">خطأ</span>
                        {% else %}
                            <span class="status-badge bg-secondary">متوقف</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>إجمالي الأخبار:</strong>
                    <span id="total-news" class="badge bg-primary">{{ collection_status.total_news or 0 }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>عدد القنوات:</strong>
                    <span id="channels-count" class="badge bg-info">{{ collection_status.channels_count or 0 }}</span>
                </div>
                
                {% if collection_status.last_update %}
                <div class="mb-3">
                    <strong>آخر تحديث:</strong>
                    <small class="text-muted d-block">{{ collection_status.last_update[:19] }}</small>
                </div>
                {% endif %}
                
                {% if collection_status.error_message %}
                <div class="alert alert-danger">
                    <small>{{ collection_status.error_message }}</small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- روابط سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-link-45deg"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('news_page') }}" class="btn btn-outline-primary">
                        <i class="bi bi-newspaper"></i>
                        عرض الأخبار
                    </a>
                    
                    {% if session.logged_in %}
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-success">
                        <i class="bi bi-speedometer2"></i>
                        لوحة التحكم
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('setup') }}" class="btn btn-outline-info">
                        <i class="bi bi-gear"></i>
                        الإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آخر الأخبار -->
{% if latest_news %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    آخر الأخبار
                </h5>
            </div>
            <div class="card-body">
                {% for news in latest_news %}
                <div class="news-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <span class="channel-tag">{{ news.channel_name }}</span>
                        <small class="text-muted">
                            {% if news.date %}
                                {{ news.date.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </small>
                    </div>
                    
                    <p class="mb-2">
                        {% if news.text|length > 200 %}
                            {{ news.text[:200] }}...
                        {% else %}
                            {{ news.text }}
                        {% endif %}
                    </p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if news.views %}
                                <small class="text-muted me-3">
                                    <i class="bi bi-eye"></i> {{ news.views }}
                                </small>
                            {% endif %}
                            {% if news.forwards %}
                                <small class="text-muted me-3">
                                    <i class="bi bi-share"></i> {{ news.forwards }}
                                </small>
                            {% endif %}
                            {% if news.replies %}
                                <small class="text-muted">
                                    <i class="bi bi-chat"></i> {{ news.replies }}
                                </small>
                            {% endif %}
                        </div>
                        
                        {% if news.has_media %}
                            <span class="badge bg-secondary">
                                <i class="bi bi-image"></i> وسائط
                            </span>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('news_page') }}" class="btn btn-primary">
                        عرض جميع الأخبار
                        <i class="bi bi-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- معلومات الاستخدام -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle"></i>
                    كيفية الاستخدام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-1-circle text-primary"></i> الإعداد الأولي</h6>
                        <ul class="list-unstyled ms-3">
                            <li>• احصل على API ID و API Hash من <a href="https://my.telegram.org/apps" target="_blank">my.telegram.org</a></li>
                            <li>• أدخل رقم هاتفك المرتبط بتلغرام</li>
                            <li>• احفظ المعلومات في ملف .env</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><i class="bi bi-2-circle text-success"></i> بدء الجمع</h6>
                        <ul class="list-unstyled ms-3">
                            <li>• سجل الدخول للوحة التحكم</li>
                            <li>• اضغط "بدء جمع الأخبار"</li>
                            <li>• راقب النتائج في الوقت الفعلي</li>
                        </ul>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6><i class="bi bi-3-circle text-info"></i> عرض النتائج</h6>
                        <ul class="list-unstyled ms-3">
                            <li>• عرض الأخبار مرتبة حسب التاريخ</li>
                            <li>• فلترة حسب القناة أو التاريخ</li>
                            <li>• عرض إحصائيات مفصلة</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><i class="bi bi-4-circle text-warning"></i> التصدير</h6>
                        <ul class="list-unstyled ms-3">
                            <li>• تصدير بصيغة CSV للتحليل</li>
                            <li>• تصدير بصيغة JSON للمطورين</li>
                            <li>• حفظ تلقائي للنسخ الاحتياطية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
