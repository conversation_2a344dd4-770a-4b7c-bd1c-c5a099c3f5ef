{% extends "base.html" %}

{% block title %}الأخبار - iNews{% endblock %}

{% block content %}
<div class="page-header text-center">
    <h1 class="display-4 fw-bold mb-3">
        <i class="fas fa-newspaper me-3"></i>
        أحدث الأخبار
    </h1>
    <p class="lead">آخر الأخبار من مصادر الفيسبوك المختارة</p>
</div>

<!-- إحصائيات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card">
            <h3 id="total-news">0</h3>
            <p class="mb-0">إجمالي الأخبار</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <h3 id="today-news">0</h3>
            <p class="mb-0">أخبار اليوم</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <h3 id="active-sources">0</h3>
            <p class="mb-0">المصادر النشطة</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <h3 id="last-update">--</h3>
            <p class="mb-0">آخر تحديث</p>
        </div>
    </div>
</div>

<!-- أدوات التحكم -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-5">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-input" placeholder="البحث في الأخبار...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="filter-source">
                    <option value="">جميع المصادر</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary w-100" onclick="refreshNews()">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث
                </button>
            </div>
            <div class="col-md-2">
                <button class="btn btn-danger w-100" onclick="clearAllNews()" title="تنظيف جميع الأخبار">
                    <i class="fas fa-trash me-2"></i>
                    تنظيف
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading -->
<div class="loading" id="loading">
    <div class="spinner-border" role="status">
        <span class="visually-hidden">جاري التحميل...</span>
    </div>
    <p class="mt-3">جاري تحميل الأخبار...</p>
</div>

<!-- قائمة الأخبار -->
<div id="news-container">
    <!-- سيتم ملؤها بـ JavaScript -->
</div>

<!-- رسالة عدم وجود أخبار -->
<div id="no-news" class="text-center py-5" style="display: none;">
    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
    <h4 class="text-muted">لا توجد أخبار متاحة</h4>
    <p class="text-muted">لم يتم العثور على أي أخبار. تأكد من إضافة مصادر في لوحة التحكم.</p>
    <a href="/dashboard" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة مصادر
    </a>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allNews = [];
let filteredNews = [];

// تحميل الأخبار عند تحميل الصفحة
$(document).ready(function() {
    loadNews();
    
    // البحث المباشر
    $('#search-input').on('input', function() {
        filterNews();
    });
    
    // فلترة حسب المصدر
    $('#filter-source').on('change', function() {
        filterNews();
    });
});

function loadNews() {
    $('#loading').show();
    $('#news-container').hide();
    $('#no-news').hide();
    
    $.get('/get_news')
        .done(function(response) {
            if (response.success) {
                allNews = response.news;
                filteredNews = allNews;
                updateStats();
                updateSourceFilter();
                displayNews();
            } else {
                showError('فشل في تحميل الأخبار: ' + response.message);
            }
        })
        .fail(function() {
            showError('خطأ في الاتصال بالخادم');
        })
        .always(function() {
            $('#loading').hide();
        });
}

function updateStats() {
    const today = new Date().toISOString().split('T')[0];
    const todayNews = allNews.filter(news => 
        news.created_time && news.created_time.startsWith(today)
    ).length;
    
    const sources = [...new Set(allNews.map(news => news.page_name))];
    
    $('#total-news').text(allNews.length);
    $('#today-news').text(todayNews);
    $('#active-sources').text(sources.length);
    
    if (allNews.length > 0) {
        const lastUpdate = new Date(Math.max(...allNews.map(news => 
            new Date(news.collected_at || news.created_time)
        )));
        $('#last-update').text(formatDate(lastUpdate));
    }
}

function updateSourceFilter() {
    const sources = [...new Set(allNews.map(news => news.page_name))];
    const select = $('#filter-source');
    
    // مسح الخيارات الحالية (عدا الأول)
    select.find('option:not(:first)').remove();
    
    // إضافة المصادر
    sources.forEach(source => {
        select.append(`<option value="${source}">${source}</option>`);
    });
}

function filterNews() {
    const searchTerm = $('#search-input').val().toLowerCase();
    const selectedSource = $('#filter-source').val();
    
    filteredNews = allNews.filter(news => {
        const matchesSearch = !searchTerm || 
            (news.message && news.message.toLowerCase().includes(searchTerm)) ||
            (news.story && news.story.toLowerCase().includes(searchTerm)) ||
            (news.page_name && news.page_name.toLowerCase().includes(searchTerm));
        
        const matchesSource = !selectedSource || news.page_name === selectedSource;
        
        return matchesSearch && matchesSource;
    });
    
    displayNews();
}

function displayNews() {
    const container = $('#news-container');
    
    if (filteredNews.length === 0) {
        container.hide();
        $('#no-news').show();
        return;
    }
    
    $('#no-news').hide();
    container.show();
    
    let html = '';
    filteredNews.forEach(news => {
        html += createNewsCard(news);
    });
    
    container.html(html);
}

function createNewsCard(news) {
    const createdTime = formatDate(new Date(news.created_time));
    const message = news.message || news.story || 'لا يوجد نص';
    const truncatedMessage = message.length > 200 ? 
        message.substring(0, 200) + '...' : message;
    
    return `
        <div class="card news-card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="card-subtitle text-primary fw-bold">
                        <i class="fab fa-facebook me-1"></i>
                        ${news.page_name}
                    </h6>
                    <small class="text-muted">${createdTime}</small>
                </div>
                
                <p class="card-text">${truncatedMessage}</p>
                
                ${news.link ? `
                    <a href="${news.link}" target="_blank" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-external-link-alt me-1"></i>
                        رابط المنشور
                    </a>
                ` : ''}
                
                <div class="row mt-3">
                    <div class="col-4 text-center">
                        <small class="text-muted">
                            <i class="fas fa-heart text-danger me-1"></i>
                            ${news.reactions_count || 0}
                        </small>
                    </div>
                    <div class="col-4 text-center">
                        <small class="text-muted">
                            <i class="fas fa-comment text-primary me-1"></i>
                            ${news.comments_count || 0}
                        </small>
                    </div>
                    <div class="col-4 text-center">
                        <small class="text-muted">
                            <i class="fas fa-share text-success me-1"></i>
                            ${news.shares_count || 0}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function formatDate(date) {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'الآن';
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    if (days < 7) return `منذ ${days} يوم`;
    
    return date.toLocaleDateString('ar-SA');
}

function refreshNews() {
    loadNews();
}

function clearAllNews() {
    if (!confirm('هل أنت متأكد من تنظيف جميع الأخبار؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    // إظهار رسالة التحميل
    const loadingAlert = `
        <div class="alert alert-info" id="clearing-alert">
            <i class="fas fa-spinner fa-spin me-2"></i>
            جاري تنظيف الأخبار...
        </div>
    `;
    $('.container').prepend(loadingAlert);

    // إرسال طلب تنظيف الأخبار
    $.ajax({
        url: '/clear_news',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({}),
        dataType: 'json'
    })
    .done(function(response) {
        $('#clearing-alert').remove();

        if (response && response.success) {
            showSuccess(response.message || 'تم تنظيف الأخبار بنجاح');
            // إعادة تحميل الأخبار لإظهار القائمة الفارغة
            loadNews();
        } else {
            const errorMsg = response && response.message ? response.message : 'خطأ غير معروف';

            // إذا كانت المشكلة تسجيل دخول، اعرض رسالة خاصة
            if (response && response.error_type === 'authentication_required') {
                showError('يجب تسجيل الدخول أولاً. <a href="/login" class="alert-link">اضغط هنا للدخول</a>');
            } else {
                showError('فشل في تنظيف الأخبار: ' + errorMsg);
            }
        }
    })
    .fail(function(xhr, status, error) {
        $('#clearing-alert').remove();
        console.error('Clear news error:', xhr, status, error);

        let errorMessage = 'خطأ في الاتصال بالخادم';

        // معالجة خاصة لخطأ 401 (تسجيل الدخول مطلوب)
        if (xhr.status === 401) {
            if (xhr.responseJSON && xhr.responseJSON.error_type === 'authentication_required') {
                showError('يجب تسجيل الدخول أولاً. <a href="/login" class="alert-link">اضغط هنا للدخول</a>');
                return;
            }
        }

        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.responseText) {
            errorMessage = 'خطأ في الخادم: ' + xhr.responseText;
        }

        showError('فشل في تنظيف الأخبار: ' + errorMessage);
    });
}

function showSuccess(message) {
    const alert = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alert);
}

function showError(message) {
    const alert = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alert);
}
</script>
{% endblock %}
