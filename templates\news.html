{% extends "base.html" %}

{% block title %}الأخبار - جامع الأخبار من تلغرام{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="bi bi-newspaper text-primary"></i>
                    الأخبار المجمعة
                </h2>
                <p class="card-text text-muted">
                    جميع الأخبار المجمعة من قنوات تلغرام
                </p>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="searchText" class="form-label">البحث في النص</label>
                        <input type="text" class="form-control" id="searchText" placeholder="ابحث في الأخبار...">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="channelFilter" class="form-label">فلترة حسب القناة</label>
                        <select class="form-select" id="channelFilter">
                            <option value="">جميع القنوات</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="dateFilter" class="form-label">فلترة حسب التاريخ</label>
                        <select class="form-select" id="dateFilter">
                            <option value="">جميع التواريخ</option>
                            <option value="today">اليوم</option>
                            <option value="yesterday">أمس</option>
                            <option value="week">آخر أسبوع</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button onclick="clearFilters()" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-newspaper display-6 text-primary"></i>
                <h4 id="totalNewsCount">{{ latest_news|length }}</h4>
                <p class="text-muted">إجمالي الأخبار</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-broadcast display-6 text-success"></i>
                <h4 id="channelsCount">{{ latest_news|map(attribute='channel_name')|unique|list|length }}</h4>
                <p class="text-muted">عدد القنوات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-eye display-6 text-info"></i>
                <h4 id="totalViews">{{ latest_news|sum(attribute='views') or 0 }}</h4>
                <p class="text-muted">إجمالي المشاهدات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-share display-6 text-warning"></i>
                <h4 id="totalShares">{{ latest_news|sum(attribute='forwards') or 0 }}</h4>
                <p class="text-muted">إجمالي المشاركات</p>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الأخبار -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    قائمة الأخبار
                </h5>
                
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortNews('date')">
                        <i class="bi bi-sort-down"></i>
                        ترتيب حسب التاريخ
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="sortNews('views')">
                        <i class="bi bi-eye"></i>
                        ترتيب حسب المشاهدات
                    </button>
                </div>
            </div>
            
            <div class="card-body">
                <div id="newsList">
                    {% if latest_news %}
                        {% for news in latest_news %}
                        <div class="news-item" data-channel="{{ news.channel_name }}" data-date="{{ news.date.strftime('%Y-%m-%d') if news.date else '' }}">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <span class="channel-tag">{{ news.channel_name }}</span>
                                    {% if news.channel_username %}
                                        <small class="text-muted">@{{ news.channel_username }}</small>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    {% if news.date %}
                                        {{ news.date.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </small>
                            </div>
                            
                            <p class="mb-3">{{ news.text }}</p>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% if news.views %}
                                        <small class="text-muted me-3">
                                            <i class="bi bi-eye"></i> {{ "{:,}".format(news.views) }}
                                        </small>
                                    {% endif %}
                                    {% if news.forwards %}
                                        <small class="text-muted me-3">
                                            <i class="bi bi-share"></i> {{ "{:,}".format(news.forwards) }}
                                        </small>
                                    {% endif %}
                                    {% if news.replies %}
                                        <small class="text-muted me-3">
                                            <i class="bi bi-chat"></i> {{ news.replies }}
                                        </small>
                                    {% endif %}
                                </div>
                                
                                <div>
                                    {% if news.has_media %}
                                        <span class="badge bg-secondary me-2">
                                            <i class="bi bi-image"></i> {{ news.media_type or 'وسائط' }}
                                        </span>
                                    {% endif %}
                                    
                                    <small class="text-muted">
                                        ID: {{ news.message_id }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="mt-3 text-muted">لا توجد أخبار</h4>
                            <p class="text-muted">لم يتم جمع أي أخبار بعد</p>
                            {% if session.logged_in %}
                                <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                                    <i class="bi bi-speedometer2"></i>
                                    اذهب للوحة التحكم
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
                
                <!-- Pagination -->
                <div id="pagination" class="d-flex justify-content-center mt-4">
                    <!-- سيتم إضافة أزرار التصفح هنا بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let allNews = {{ latest_news|tojson if latest_news else '[]' }};
let filteredNews = [...allNews];
let currentPage = 1;
const itemsPerPage = 10;

// تحميل قائمة القنوات في الفلتر
document.addEventListener('DOMContentLoaded', function() {
    const channelFilter = document.getElementById('channelFilter');
    const channels = [...new Set(allNews.map(news => news.channel_name))];
    
    channels.forEach(channel => {
        const option = document.createElement('option');
        option.value = channel;
        option.textContent = channel;
        channelFilter.appendChild(option);
    });
    
    // إضافة مستمعي الأحداث للفلاتر
    document.getElementById('searchText').addEventListener('input', filterNews);
    document.getElementById('channelFilter').addEventListener('change', filterNews);
    document.getElementById('dateFilter').addEventListener('change', filterNews);
    
    displayNews();
});

function filterNews() {
    const searchText = document.getElementById('searchText').value.toLowerCase();
    const channelFilter = document.getElementById('channelFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    
    filteredNews = allNews.filter(news => {
        // فلترة النص
        const textMatch = !searchText || news.text.toLowerCase().includes(searchText);
        
        // فلترة القناة
        const channelMatch = !channelFilter || news.channel_name === channelFilter;
        
        // فلترة التاريخ
        let dateMatch = true;
        if (dateFilter && news.date) {
            const newsDate = new Date(news.date);
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const weekAgo = new Date(today);
            weekAgo.setDate(weekAgo.getDate() - 7);
            
            switch(dateFilter) {
                case 'today':
                    dateMatch = newsDate.toDateString() === today.toDateString();
                    break;
                case 'yesterday':
                    dateMatch = newsDate.toDateString() === yesterday.toDateString();
                    break;
                case 'week':
                    dateMatch = newsDate >= weekAgo;
                    break;
            }
        }
        
        return textMatch && channelMatch && dateMatch;
    });
    
    currentPage = 1;
    displayNews();
}

function sortNews(criteria) {
    filteredNews.sort((a, b) => {
        if (criteria === 'date') {
            return new Date(b.date || 0) - new Date(a.date || 0);
        } else if (criteria === 'views') {
            return (b.views || 0) - (a.views || 0);
        }
        return 0;
    });
    
    displayNews();
}

function displayNews() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const newsToShow = filteredNews.slice(startIndex, endIndex);
    
    const newsList = document.getElementById('newsList');
    
    if (newsToShow.length === 0) {
        newsList.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-search display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد نتائج</h4>
                <p class="text-muted">جرب تغيير معايير البحث</p>
            </div>
        `;
        document.getElementById('pagination').innerHTML = '';
        return;
    }
    
    newsList.innerHTML = newsToShow.map(news => `
        <div class="news-item">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div>
                    <span class="channel-tag">${news.channel_name}</span>
                    ${news.channel_username ? `<small class="text-muted">@${news.channel_username}</small>` : ''}
                </div>
                <small class="text-muted">
                    ${news.date ? new Date(news.date).toLocaleString('ar-SA') : 'غير محدد'}
                </small>
            </div>
            
            <p class="mb-3">${news.text}</p>
            
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    ${news.views ? `<small class="text-muted me-3"><i class="bi bi-eye"></i> ${news.views.toLocaleString()}</small>` : ''}
                    ${news.forwards ? `<small class="text-muted me-3"><i class="bi bi-share"></i> ${news.forwards.toLocaleString()}</small>` : ''}
                    ${news.replies ? `<small class="text-muted me-3"><i class="bi bi-chat"></i> ${news.replies}</small>` : ''}
                </div>
                
                <div>
                    ${news.has_media ? `<span class="badge bg-secondary me-2"><i class="bi bi-image"></i> ${news.media_type || 'وسائط'}</span>` : ''}
                    <small class="text-muted">ID: ${news.message_id}</small>
                </div>
            </div>
        </div>
    `).join('');
    
    displayPagination();
}

function displayPagination() {
    const totalPages = Math.ceil(filteredNews.length / itemsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '<nav><ul class="pagination">';
    
    // زر السابق
    if (currentPage > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${currentPage - 1})">السابق</a></li>`;
    }
    
    // أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage) {
            paginationHTML += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
        }
    }
    
    // زر التالي
    if (currentPage < totalPages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${currentPage + 1})">التالي</a></li>`;
    }
    
    paginationHTML += '</ul></nav>';
    pagination.innerHTML = paginationHTML;
}

function changePage(page) {
    currentPage = page;
    displayNews();
    window.scrollTo(0, 0);
}

function clearFilters() {
    document.getElementById('searchText').value = '';
    document.getElementById('channelFilter').value = '';
    document.getElementById('dateFilter').value = '';
    filteredNews = [...allNews];
    currentPage = 1;
    displayNews();
}
</script>
{% endblock %}
