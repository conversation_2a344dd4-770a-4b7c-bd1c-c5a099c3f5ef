# 🔧 حل مشكلة Facebook API - iNews

## 📊 تشخيص المشكلة

✅ **ما يعمل:**
- App ID و App Secret صحيحان
- Access Token يتم الحصول عليه بنجاح
- التطبيق مسجل بشكل صحيح

❌ **المشكلة:**
```
Error Code: 100
Message: "This endpoint requires the 'pages_read_engagement' permission or the 'Page Public Content Access' feature"
```

## 🎯 السبب الجذري

Facebook غير سياساته في 2021-2022 ولم تعد الصلاحيات التالية متاحة بسهولة:
- `pages_read_engagement`
- `Page Public Content Access`
- `Page Public Metadata Access`

## 🛠️ الحلول المتاحة

### **الحل الأول: طلب مراجعة من Facebook (الأصعب)**

#### **الخطوات:**
1. **اذهب إلى Facebook Developers Console:**
   ```
   https://developers.facebook.com/apps/1905779223574690/
   ```

2. **في قسم "App Review":**
   - اطلب `Page Public Content Access`
   - اطلب `pages_read_engagement`
   - املأ نموذج التبرير

3. **التبرير المطلوب:**
   ```
   الغرض: نظام إدارة أخبار شخصي
   الاستخدام: جمع الأخبار من الصفحات العامة فقط
   البيانات: المنشورات العامة والتفاعلات
   المستخدمون: شخصي/محدود
   ```

4. **انتظار الموافقة:** قد يستغرق أسابيع

### **الحل الثاني: استخدام صفحات تملكها (الأسهل)**

#### **إنشاء صفحة فيسبوك خاصة بك:**
1. **أنشئ صفحة فيسبوك جديدة:**
   ```
   https://www.facebook.com/pages/create/
   ```

2. **اجعلها صفحة أخبار:**
   - الاسم: "أخبار [اسمك]"
   - الفئة: "إعلام/أخبار"
   - اجعلها عامة

3. **احصل على Page Access Token:**
   ```
   https://developers.facebook.com/tools/explorer/
   - اختر صفحتك
   - اطلب page_access_token
   ```

4. **استخدم Page Token بدلاً من App Token**

### **الحل الثالث: استخدام Graph API Explorer (مؤقت)**

#### **للاختبار السريع:**
1. **اذهب إلى Graph API Explorer:**
   ```
   https://developers.facebook.com/tools/explorer/
   ```

2. **احصل على User Access Token:**
   - اختر التطبيق
   - أضف الصلاحيات المتاحة
   - انسخ الـ Token

3. **استخدم الـ Token في التطبيق مؤقتاً**

### **الحل الرابع: تحديث الكود للتعامل مع القيود (الأفضل)**

#### **تحديث inews_collector.py:**

```python
def get_access_token_with_fallback(self):
    """الحصول على Access Token مع خيارات متعددة"""
    
    # جرب Page Access Token أولاً (إذا كان متوفر)
    page_token = os.getenv('FACEBOOK_PAGE_ACCESS_TOKEN')
    if page_token:
        return page_token
    
    # جرب User Access Token
    user_token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    if user_token:
        return user_token
    
    # الطريقة التقليدية (App Token)
    return self.get_access_token()
```

## 🔄 الحل المؤقت الفوري

### **تحديث ملف .env:**
```env
# بيانات Facebook الحالية
FACEBOOK_APP_ID=1905779223574690
FACEBOOK_APP_SECRET=your_app_secret

# إضافة tokens بديلة (احصل عليها من Graph API Explorer)
FACEBOOK_USER_ACCESS_TOKEN=your_user_token_here
FACEBOOK_PAGE_ACCESS_TOKEN=your_page_token_here
```

### **تحديث الكود:**
```python
# في inews_collector.py
def __init__(self):
    # جرب tokens مختلفة
    self.user_token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    self.page_token = os.getenv('FACEBOOK_PAGE_ACCESS_TOKEN')
    # ... باقي الكود
```

## 📋 خطة العمل الموصى بها

### **المرحلة الأولى (فوري):**
1. ✅ **احصل على User Access Token** من Graph API Explorer
2. ✅ **أضفه لملف .env**
3. ✅ **اختبر مع صفحة واحدة**

### **المرحلة الثانية (قصيرة المدى):**
1. 🔄 **أنشئ صفحة فيسبوك خاصة بك**
2. 🔄 **احصل على Page Access Token**
3. 🔄 **استخدمها كمصدر أساسي**

### **المرحلة الثالثة (طويلة المدى):**
1. 📝 **قدم طلب مراجعة لـ Facebook**
2. 📝 **انتظر الموافقة**
3. 📝 **استخدم الصلاحيات الكاملة**

## 🎯 الحل السريع الآن

### **الخطوات العملية:**

#### **1. احصل على User Access Token:**
```
1. اذهب إلى: https://developers.facebook.com/tools/explorer/
2. اختر تطبيق "inews"
3. اضغط "Generate Access Token"
4. انسخ الـ Token
```

#### **2. أضف الـ Token لملف .env:**
```env
FACEBOOK_USER_ACCESS_TOKEN=EAAb...
```

#### **3. حدث inews_collector.py:**
```python
def get_access_token(self):
    # جرب User Token أولاً
    user_token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    if user_token:
        return user_token
    
    # الطريقة التقليدية
    # ... باقي الكود
```

#### **4. اختبر مع صفحة عامة:**
```python
# جرب مع صفحة مشهورة
page_id = "facebook"  # صفحة Facebook الرسمية
```

## ⚠️ ملاحظات مهمة

### **قيود User Access Token:**
- ⏰ **ينتهي خلال ساعات** (يحتاج تجديد)
- 👤 **مرتبط بحسابك الشخصي**
- 🔒 **صلاحيات محدودة**

### **قيود Page Access Token:**
- 📄 **يعمل مع صفحاتك فقط**
- ⏰ **صالح لفترة أطول**
- 🔓 **صلاحيات أكثر**

### **الحل الدائم:**
- 📝 **طلب مراجعة Facebook** هو الحل الوحيد الدائم
- ⏳ **قد يستغرق وقت طويل**
- 📋 **يحتاج تبرير قوي**

---

## 🚀 ابدأ الآن

**الخطوة التالية:** احصل على User Access Token من Graph API Explorer وأضفه لملف .env

**الهدف:** جعل Facebook API يعمل مؤقتاً حتى نحصل على الصلاحيات الكاملة
