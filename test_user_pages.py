#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوصول للصفحات المتابعة في حساب المستخدم
"""

import requests
import json
from dotenv import load_dotenv
import os

# تحميل متغيرات البيئة
load_dotenv()

def test_user_endpoints():
    """اختبار endpoints مختلفة للمستخدم"""
    token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    
    if not token:
        print("❌ لا يوجد User Access Token في ملف .env")
        return
    
    print("🔍 اختبار الوصول للصفحات المتابعة...")
    print("=" * 60)
    
    # قائمة endpoints للاختبار
    endpoints = [
        {
            'path': '/me',
            'description': 'معلومات المستخدم الأساسية',
            'params': {'fields': 'id,name,email'}
        },
        {
            'path': '/me/likes',
            'description': 'الصفحات المعجب بها',
            'params': {'limit': 10}
        },
        {
            'path': '/me/feed',
            'description': 'خلاصة المستخدم',
            'params': {'limit': 5}
        },
        {
            'path': '/me/home',
            'description': 'الصفحة الرئيسية',
            'params': {'limit': 5}
        },
        {
            'path': '/me/posts',
            'description': 'منشورات المستخدم',
            'params': {'limit': 5}
        },
        {
            'path': '/me/subscriptions',
            'description': 'الاشتراكات',
            'params': {'limit': 10}
        }
    ]
    
    successful_endpoints = []
    
    for endpoint in endpoints:
        print(f"\n📍 اختبار: {endpoint['description']}")
        print(f"🔗 المسار: {endpoint['path']}")
        
        try:
            url = f"https://graph.facebook.com/v18.0{endpoint['path']}"
            params = {'access_token': token}
            params.update(endpoint['params'])
            
            response = requests.get(url, params=params, timeout=10)
            
            print(f"📊 رمز الاستجابة: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    if 'data' in data:
                        items = data['data']
                        print(f"✅ نجح! عدد العناصر: {len(items)}")
                        
                        if items:
                            first_item = items[0]
                            print(f"📄 مفاتيح أول عنصر: {list(first_item.keys())}")
                            
                            # عرض تفاصيل مفيدة
                            if 'name' in first_item:
                                print(f"📝 اسم أول عنصر: {first_item['name']}")
                            if 'message' in first_item:
                                print(f"💬 رسالة: {first_item['message'][:100]}...")
                            if 'created_time' in first_item:
                                print(f"⏰ وقت الإنشاء: {first_item['created_time']}")
                        
                        successful_endpoints.append(endpoint)
                    else:
                        # للـ endpoints التي ترجع object وليس array
                        print(f"✅ نجح! البيانات: {list(data.keys())}")
                        if 'name' in data:
                            print(f"📝 الاسم: {data['name']}")
                        successful_endpoints.append(endpoint)
                        
                except json.JSONDecodeError:
                    print(f"❌ خطأ في تحليل JSON")
                    
            elif response.status_code == 400:
                error_data = response.json() if response.content else {}
                error_message = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"❌ خطأ 400: {error_message}")
                
            elif response.status_code == 403:
                print(f"🔒 ممنوع: يحتاج صلاحيات إضافية")
                
            else:
                print(f"❌ خطأ {response.status_code}: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج")
    print("=" * 60)
    
    if successful_endpoints:
        print(f"✅ نجح {len(successful_endpoints)} من {len(endpoints)} endpoint:")
        for endpoint in successful_endpoints:
            print(f"   • {endpoint['description']} - {endpoint['path']}")
    else:
        print("❌ لم ينجح أي endpoint")
    
    return successful_endpoints

def test_specific_pages():
    """اختبار الوصول لصفحات محددة"""
    token = os.getenv('FACEBOOK_USER_ACCESS_TOKEN')
    
    print("\n" + "=" * 60)
    print("📄 اختبار الوصول لصفحات محددة")
    print("=" * 60)
    
    # صفحات مشهورة للاختبار
    test_pages = [
        {'id': 'facebook', 'name': 'Facebook'},
        {'id': 'meta', 'name': 'Meta'},
        {'id': 'microsoft', 'name': 'Microsoft'},
        {'id': 'google', 'name': 'Google'},
    ]
    
    for page in test_pages:
        print(f"\n🔍 اختبار صفحة: {page['name']} ({page['id']})")
        
        try:
            # اختبار الوصول الأساسي للصفحة
            url = f"https://graph.facebook.com/v18.0/{page['id']}"
            params = {
                'access_token': token,
                'fields': 'id,name,category,fan_count,about'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ معلومات الصفحة:")
                print(f"   📝 الاسم: {data.get('name', 'غير محدد')}")
                print(f"   📂 الفئة: {data.get('category', 'غير محدد')}")
                print(f"   👥 المتابعون: {data.get('fan_count', 'غير محدد')}")
                
                # اختبار الوصول للمنشورات
                posts_url = f"https://graph.facebook.com/v18.0/{page['id']}/posts"
                posts_params = {
                    'access_token': token,
                    'fields': 'id,message,created_time',
                    'limit': 3
                }
                
                posts_response = requests.get(posts_url, params=posts_params, timeout=10)
                
                if posts_response.status_code == 200:
                    posts_data = posts_response.json()
                    posts = posts_data.get('data', [])
                    print(f"   📰 المنشورات: {len(posts)} منشور متاح")
                else:
                    print(f"   ❌ لا يمكن الوصول للمنشورات: {posts_response.status_code}")
                    
            else:
                error_data = response.json() if response.content else {}
                error_message = error_data.get('error', {}).get('message', 'خطأ غير معروف')
                print(f"❌ فشل: {error_message}")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")

def suggest_solutions():
    """اقتراح حلول بناء على النتائج"""
    print("\n" + "=" * 60)
    print("💡 الحلول المقترحة")
    print("=" * 60)
    
    print("🎯 بناء على النتائج أعلاه:")
    print()
    print("1️⃣ **إذا نجح /me/feed:**")
    print("   • يمكن جلب الأخبار من خلاصتك الشخصية")
    print("   • تحتوي على منشورات الصفحات المتابعة")
    print("   • الحل الأمثل لاحتياجاتك")
    print()
    print("2️⃣ **إذا نجح /me/likes:**")
    print("   • يمكن الحصول على قائمة الصفحات المعجب بها")
    print("   • ثم محاولة الوصول لكل صفحة منفردة")
    print("   • قد يحتاج صلاحيات إضافية")
    print()
    print("3️⃣ **إذا فشلت جميع الطرق:**")
    print("   • استخدام RSS feeds كبديل")
    print("   • إنشاء صفحة شخصية")
    print("   • طلب مراجعة من Facebook")
    print()
    print("🔧 **الخطوة التالية:**")
    print("   • راجع النتائج أعلاه")
    print("   • اختر الحل المناسب")
    print("   • سأحدث الكود وفقاً للنتائج")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الوصول للصفحات المتابعة")
    print("=" * 60)
    print("🎯 الهدف: جلب الأخبار من الصفحات المشترك بها")
    print("=" * 60)
    
    # اختبار endpoints المستخدم
    successful = test_user_endpoints()
    
    # اختبار صفحات محددة
    test_specific_pages()
    
    # اقتراح حلول
    suggest_solutions()
    
    print("\n🎉 انتهى الاختبار!")
    print("📋 راجع النتائج أعلاه لتحديد أفضل طريقة للمتابعة")

if __name__ == "__main__":
    main()
