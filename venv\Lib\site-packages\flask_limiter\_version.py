
# This file was generated by 'versioneer.py' (0.22) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-08-30T17:31:50-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "dfcd51025dace1cf17c8b19b5c5844d1c2cfb5d8",
 "version": "3.5.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
