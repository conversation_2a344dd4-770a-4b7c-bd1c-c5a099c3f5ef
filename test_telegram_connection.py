#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بتلغرام مباشرة
"""

import asyncio
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

async def test_telegram():
    """اختبار الاتصال بتلغرام"""
    print("🔍 اختبار الاتصال بتلغرام...")
    print("=" * 50)
    
    # قراءة البيانات من .env
    api_id = os.getenv('TELEGRAM_API_ID')
    api_hash = os.getenv('TELEGRAM_API_HASH')
    phone = os.getenv('TELEGRAM_PHONE_NUMBER')
    
    print(f"📱 API ID: {api_id}")
    print(f"🔑 API Hash: {api_hash[:10]}..." if api_hash else "غير محدد")
    print(f"📞 رقم الهاتف: {phone}")
    print()
    
    if not all([api_id, api_hash, phone]):
        print("❌ بيانات تلغرام API مفقودة في ملف .env")
        print("💡 تأكد من وجود:")
        print("   TELEGRAM_API_ID=your_api_id")
        print("   TELEGRAM_API_HASH=your_api_hash")
        print("   TELEGRAM_PHONE_NUMBER=+9647714366")
        return False
    
    try:
        # محاولة استيراد telethon
        from telethon import TelegramClient
        print("✅ تم استيراد مكتبة Telethon بنجاح")
    except ImportError:
        print("❌ مكتبة Telethon غير مثبتة")
        print("💡 شغل: pip install telethon")
        return False
    
    try:
        # إنشاء عميل تلغرام
        client = TelegramClient('test_session', api_id, api_hash)
        print("✅ تم إنشاء عميل تلغرام")
        
        print("🔄 محاولة الاتصال...")
        
        # بدء الاتصال
        await client.start(phone=phone)
        print("✅ تم الاتصال بتلغرام بنجاح!")
        
        # الحصول على معلومات المستخدم
        me = await client.get_me()
        print(f"👤 مرحباً {me.first_name}!")
        print(f"🆔 معرف المستخدم: {me.id}")
        print(f"📱 رقم الهاتف: {me.phone}")
        
        # جلب قائمة الحوارات (القنوات والمجموعات)
        print("\n📋 جلب قائمة القنوات...")
        
        channels = []
        async for dialog in client.iter_dialogs(limit=20):
            if hasattr(dialog.entity, 'broadcast') and dialog.entity.broadcast:
                channels.append({
                    'name': dialog.name,
                    'id': dialog.entity.id,
                    'username': getattr(dialog.entity, 'username', None),
                    'participants': getattr(dialog.entity, 'participants_count', 0)
                })
        
        print(f"✅ تم العثور على {len(channels)} قناة")
        
        if channels:
            print("\n📺 أول 10 قنوات:")
            for i, channel in enumerate(channels[:10], 1):
                username = f"@{channel['username']}" if channel['username'] else "بدون معرف"
                print(f"   {i}. {channel['name']} ({username})")
        
        # اختبار جلب رسائل من قناة
        if channels:
            test_channel = channels[0]
            print(f"\n🧪 اختبار جلب رسائل من: {test_channel['name']}")
            
            messages = []
            async for message in client.iter_messages(test_channel['id'], limit=5):
                if message.text:
                    messages.append({
                        'text': message.text[:100] + "..." if len(message.text) > 100 else message.text,
                        'date': message.date
                    })
            
            print(f"✅ تم جلب {len(messages)} رسالة")
            
            if messages:
                print("📝 آخر الرسائل:")
                for i, msg in enumerate(messages, 1):
                    print(f"   {i}. {msg['text']}")
        
        # إغلاق الاتصال
        await client.disconnect()
        print("\n✅ تم إغلاق الاتصال بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        
        # تشخيص الأخطاء الشائعة
        error_str = str(e).lower()
        
        if 'phone number invalid' in error_str:
            print("💡 الحل: تأكد من صيغة رقم الهاتف (+9647714367586)")
        elif 'api_id' in error_str or 'api_hash' in error_str:
            print("💡 الحل: تأكد من صحة API ID و API Hash")
        elif 'flood' in error_str:
            print("💡 الحل: انتظر قليلاً ثم أعد المحاولة")
        elif 'session' in error_str:
            print("💡 الحل: احذف ملف test_session.session وأعد المحاولة")
        else:
            print("💡 الحل: تحقق من اتصال الإنترنت وأعد المحاولة")
        
        try:
            await client.disconnect()
        except:
            pass
        
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 اختبار اتصال تلغرام - رقمك الشخصي")
    print("=" * 60)
    print("📞 رقم الهاتف: +9647714366758")
    print("🎯 الهدف: التأكد من عمل الاتصال مع تلغرام")
    print("=" * 60)
    
    try:
        # تشغيل الاختبار
        result = asyncio.run(test_telegram())
        
        if result:
            print("\n🎉 نجح الاختبار! يمكنك الآن استخدام التطبيق")
            print("🚀 شغل التطبيق: python app.py")
            print("🌐 اذهب لـ: http://localhost:5020")
        else:
            print("\n❌ فشل الاختبار")
            print("🔧 راجع الأخطاء أعلاه وأصلحها")
            
    except KeyboardInterrupt:
        print("\n🛑 تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    # تنظيف ملفات الجلسة التجريبية
    try:
        if os.path.exists('test_session.session'):
            os.remove('test_session.session')
            print("🧹 تم حذف ملف الجلسة التجريبي")
    except:
        pass

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
